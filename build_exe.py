#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyInstaller打包脚本
将mail139_swagger_api.py打包成无窗口的单个exe文件
"""

import os
import sys
import subprocess

def build_exe():
    """构建exe文件"""
    print("开始打包mail139_swagger_api.py为exe文件...")
    
    # 当前目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    main_file = os.path.join(current_dir, "mail139_swagger_api.py")
    
    if not os.path.exists(main_file):
        print(f"错误: 找不到文件 {main_file}")
        return False
    
    # PyInstaller命令
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个文件
        "--noconsole",                  # 无控制台窗口
        "--name=Mail139API",            # 输出文件名
        "--distpath=dist",              # 输出目录
        "--workpath=build",             # 临时文件目录
        "--specpath=.",                 # spec文件目录
        "--clean",                      # 清理临时文件
        "--noconfirm",                  # 不询问覆盖
        main_file
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        # 执行打包命令
        result = subprocess.run(cmd, cwd=current_dir, capture_output=True, text=True)
        
        if result.returncode == 0:
            exe_path = os.path.join(current_dir, "dist", "Mail139API.exe")
            if os.path.exists(exe_path):
                print(f"✅ 打包成功!")
                print(f"📁 输出文件: {exe_path}")
                print(f"📊 文件大小: {os.path.getsize(exe_path) / 1024 / 1024:.2f} MB")
                return True
            else:
                print("❌ 打包失败: 找不到输出文件")
                return False
        else:
            print("❌ 打包失败:")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except FileNotFoundError:
        print("❌ 错误: 找不到pyinstaller命令")
        print("请先安装pyinstaller: pip install pyinstaller")
        return False
    except Exception as e:
        print(f"❌ 打包过程中出现错误: {e}")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装PyInstaller...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        print("✅ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ PyInstaller安装失败")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("Mail139 API 打包工具")
    print("=" * 60)
    
    # 检查是否安装了PyInstaller
    try:
        subprocess.run(["pyinstaller", "--version"], capture_output=True, check=True)
        print("✅ PyInstaller已安装")
    except (FileNotFoundError, subprocess.CalledProcessError):
        print("⚠️  PyInstaller未安装，正在安装...")
        if not install_pyinstaller():
            sys.exit(1)
    
    # 开始打包
    if build_exe():
        print("\n🎉 打包完成!")
        print("📝 使用说明:")
        print("  1. 运行 Mail139API.exe 启动服务")
        print("  2. 服务地址: http://localhost:5000")
        print("  3. API文档: http://localhost:5000/docs/")
        print("  4. 程序将在后台运行，无窗口显示")
    else:
        print("\n❌ 打包失败，请检查错误信息")
        sys.exit(1)