#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
139邮箱自动化API - 带Swagger UI文档
提供登录和发送邮件的RESTful API接口，集成Swagger UI文档
"""
from pydoc import html

from flask import Flask, request
from flask_restx import Api, Resource, fields
from flask_cors import CORS
import time
import random
import requests
import urllib3
import json
import base64
import logging
import hashlib
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.backends import default_backend

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 配置Swagger UI
api = Api(
    app,
    version='1.0.0',
    title='139邮箱自动化API',
    description='基于逆向工程的139邮箱完全自动化解决方案，支持HTTP和SOCKS5代理',
    doc='/docs/',  # Swagger UI访问路径
    contact='sweep_monk',
    contact_url='https://gitee.com/sweep_monk/139mail'
)

# 创建命名空间
ns_auth = api.namespace('auth', description='认证相关接口')
ns_mail = api.namespace('mail', description='邮件相关接口')
ns_utils = api.namespace('utils', description='工具接口')

# 统一的加密请求模型 - 直接传输加密字符串
encrypted_request_model = api.model('EncryptedRequest', {
    'data': fields.String(required=True, description='AES加密的请求数据（Base64编码的加密字符串）', 
                         example='U2FsdGVkX1+vupppZksvRf5pq5g5XjFRIipRkwB0K1Y96Qsv2Lm+31cmzaAILwyt')
})

# 加密响应模型 - JSON格式包装的加密数据
encrypted_response_model = api.model('EncryptedResponse', {
    'data': fields.String(description='AES加密的响应数据（Base64编码的加密字符串）', 
                         example='U2FsdGVkX1+vupppZksvRf5pq5g5XjFRIipRkwB0K1Y96Qsv2Lm+31cmzaAILwyt')
})

error_response = api.model('ErrorResponse', {
    'msg': fields.String(description='错误消息', example='请求参数错误')
})


class AESCrypto:
    """AES加密工具类"""
    
    def __init__(self, key=None):
        if key is None:
            self.key = b'139mail_api_2025_secret_key_3200'  # 32字节密钥
        else:
            # 确保密钥长度为32字节
            if isinstance(key, str):
                key = key.encode('utf-8')
            self.key = key
        
        # 确保密钥长度为32字节
        if len(self.key) < 32:
            self.key = self.key.ljust(32, b'0')
        elif len(self.key) > 32:
            self.key = self.key[:32]
    
    def encrypt(self, plaintext):
        """AES加密 - 使用ECB模式"""
        try:
            if isinstance(plaintext, str):
                plaintext = plaintext.encode('utf-8')
            
            # 使用ECB模式，不需要IV
            cipher = Cipher(algorithms.AES(self.key), modes.ECB(), backend=default_backend())
            encryptor = cipher.encryptor()
            
            # 填充数据
            padder = padding.PKCS7(128).padder()
            padded_data = padder.update(plaintext)
            padded_data += padder.finalize()
            
            # 加密
            ciphertext = encryptor.update(padded_data) + encryptor.finalize()
            
            # 直接base64编码密文
            return base64.b64encode(ciphertext).decode('utf-8')
            
        except Exception as e:
            logger.error(f"❌ AES加密失败: {e}")
            return None
    
    def decrypt(self, encrypted_data):
        """AES解密 - 使用ECB模式"""
        try:
            if isinstance(encrypted_data, str):
                encrypted_data = base64.b64decode(encrypted_data.encode('utf-8'))
            
            # 使用ECB模式解密，不需要IV
            cipher = Cipher(algorithms.AES(self.key), modes.ECB(), backend=default_backend())
            decryptor = cipher.decryptor()
            
            # 解密
            padded_plaintext = decryptor.update(encrypted_data) + decryptor.finalize()
            
            # 去除填充
            unpadder = padding.PKCS7(128).unpadder()
            plaintext = unpadder.update(padded_plaintext)
            plaintext += unpadder.finalize()
            
            return plaintext.decode('utf-8')
            
        except Exception as e:
            logger.error(f"❌ AES解密失败: {e}")
            return None

# 全局AES加密实例
aes_crypto = AESCrypto()

def create_encrypted_response(data):
    """创建加密响应，返回JSON格式包装的加密数据"""
    try:
        # 将数据转换为JSON字符串
        json_str = json.dumps(data, ensure_ascii=False)
        # 使用固定密钥加密
        encrypted_data = aes_crypto.encrypt(json_str)
        # 返回JSON格式包装的加密数据
        return {"data": encrypted_data}
    except Exception as e:
        # 如果加密失败，返回错误信息的加密版本
        error_data = {"msg": f"响应加密失败: {str(e)}"}
        json_str = json.dumps(error_data, ensure_ascii=False)
        encrypted_error = aes_crypto.encrypt(json_str)
        return {"data": encrypted_error}

def decrypt_request_data(encrypted_data):
    """解密请求数据，使用固定密钥"""
    try:
        # 使用固定密钥解密
        decrypted_json = aes_crypto.decrypt(encrypted_data)
        if not decrypted_json:
            return None, "数据解密失败"
        
        # 解析JSON
        try:
            data = json.loads(decrypted_json)
            return data, None
        except json.JSONDecodeError:
            return None, "解密后的数据格式错误，需要JSON格式"
    except Exception as e:
        return None, f"解密过程出错: {str(e)}"

class Mail139Client:
    def __init__(self, proxy_type=None, proxy_info=None):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0'
        })
        self.session.verify = False
        
        # 设置代理超时时间为3秒
        self.timeout = 10
        
        # 会话信息初始化
        self.os_sso_sid = None
        self.rmkey = None
        self.username = None
        
        # 设置代理
        if proxy_type and proxy_info:
            if not self.setup_proxy(proxy_type, proxy_info):
                raise Exception("代理设置失败，无法继续执行")
    
    def padding(self, n, m=2):
        """
        数字前置补零函数（复现JavaScript的padding逻辑）
        与 http_request_script.py 保持一致

        Args:
            n: 要补零的数字
            m: 目标长度（默认2位）

        Returns:
            str: 补零后的字符串
        """
        import math

        # JavaScript: 1 + Math.floor(Math.log(n | 1) / Math.LN10 + 10e-16)
        if n == 0:
            actual_digits = 1
        else:
            # 使用JavaScript相同的计算方式
            actual_digits = int(1 + math.floor(math.log10(abs(n | 1)) + 1e-15))

        zeros_needed = m - actual_digits
        if zeros_needed > 0:
            return "0" * zeros_needed + str(n)
        else:
            return str(n)
    
    def generate_cguid(self):
        """
        生成CGUID（复现JavaScript的getCGUID算法）
        格式: HHMMSSmmm9999
        与 http_request_script.py 保持一致

        Returns:
            str: 13位CGUID字符串
        """
        from datetime import datetime

        now = datetime.now()

        # 获取时间组件
        hours = self.padding(now.hour, 2)           # HH: 00-23
        minutes = self.padding(now.minute, 2)       # MM: 00-59
        seconds = self.padding(now.second, 2)       # SS: 00-59
        milliseconds = self.padding(now.microsecond // 1000, 3)  # mmm: 000-999

        # JavaScript: Math.ceil(Math.random() * 9999)
        # 生成1-9999的随机数，补齐4位
        random_part = self.padding(random.randint(1, 9999), 4)

        cguid = hours + minutes + seconds + milliseconds + random_part
        return cguid

    def generate_randnum(self):
        """
        生成randnum（复现JavaScript的Math.random()）
        与 http_request_script.py 保持一致

        Returns:
            str: 0-1之间的随机浮点数字符串
        """
        return str(random.random())
    
    def encrypt_139_password(self, password):
        """
        139邮箱密码加密算法（SHA-1）
        对应JavaScript中的calcDigest函数
        与 http_request_script.py 保持一致

        Args:
            password (str): 用户输入的原始密码

        Returns:
            str: 加密后的40位十六进制SHA-1哈希值
        """
        # 添加139邮箱特有的盐值前缀
        salted_password = "fetion.com.cn:" + password

        # 使用SHA-1进行加密
        sha1_hash = hashlib.sha1(salted_password.encode('utf-8'))

        # 返回40位十六进制字符串
        return sha1_hash.hexdigest()
    
    def calculate_md5(self, text):
        """计算MD5值"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()
    
    def get_captcha(self, redirect_url=None):
        """获取验证码图片"""
        try:
            logger.info("🔍 开始获取验证码图片...")
            
            # 直接请求验证码图片API（参考http_request_script.py的实现）
            import random
            random_param = random.random()
            captcha_url = f"https://imagecode1.mail.10086.cn/getimage?clientid=1&r={random_param}"
            logger.info(f"🔍 请求验证码图片API: {captcha_url}")
            
            # 设置请求头（参考http_request_script.py）
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br, zstd',
                'pragma': 'no-cache',
                'cache-control': 'no-cache',
                'sec-ch-ua-platform': '"Windows"',
                'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
                'sec-ch-ua-mobile': '?0',
                'sec-fetch-site': 'same-site',
                'sec-fetch-mode': 'no-cors',
                'sec-fetch-dest': 'image',
                'referer': 'https://mail.10086.cn/',
                'accept-language': 'zh-CN,zh;q=0.9',
                'priority': 'i'
            }
            
            logger.info(f"🔍 验证码请求URL: {captcha_url}")
            
            # 发送请求
            response = self.session.get(captcha_url, headers=headers, timeout=self.timeout)
            
            logger.info(f"🔍 验证码图片响应状态码: {response.status_code}")
            logger.info(f"🔍 Content-Type: {response.headers.get('content-type', 'N/A')}")
            
            if response.status_code == 200:
                # 将图片内容转换为base64
                import base64
                captcha_image_base64 = base64.b64encode(response.content).decode('utf-8')
                logger.info(f"🔍 验证码图片base64长度: {len(captcha_image_base64)}")

                # 从Set-Cookie头中提取agentid（参考http_request_script.py的实现）
                captcha_text = None
                agentid_cookies = []
                captcha_text = None
                
                # 从Set-Cookie头中提取agentid
                set_cookies = []
                # 获取所有Set-Cookie头
                if hasattr(response.headers, 'get_list'):
                    set_cookies = response.headers.get_list('Set-Cookie') or []
                else:
                    # 如果get_list不可用，获取单个Set-Cookie头
                    set_cookie_header = response.headers.get('Set-Cookie', '')
                    if set_cookie_header:
                        set_cookies = [set_cookie_header]
                
                # 如果还是没有，尝试从response.cookies中获取
                if not set_cookies and hasattr(response, 'cookies'):
                    for cookie in response.cookies:
                        if cookie.name == 'agentid':
                            # 构造Set-Cookie格式的字符串
                            cookie_str = f"agentid={cookie.value}"
                            set_cookies.append(cookie_str)
                            break
                
                logger.info(f"🔍 响应头中的Set-Cookie: {set_cookies}")
                
                for cookie_header in set_cookies:
                    if 'agentid=' in cookie_header:
                        agentid_cookies.append(cookie_header)
                
                # 解析agentid获取验证码文字
                if agentid_cookies:
                    # 取第一个agentid cookie
                    agentid_cookie = agentid_cookies[0]
                    # 提取agentid值
                    agentid_start = agentid_cookie.find('agentid=') + 8
                    agentid_end = agentid_cookie.find(';', agentid_start)
                    if agentid_end == -1:
                        agentid_end = len(agentid_cookie)
                    
                    agentid_value = agentid_cookie[agentid_start:agentid_end]
                    logger.info(f"🔍 提取的agentid: {agentid_value}")
                    
                    # 分割UUID和编码文字
                    if '&' in agentid_value:
                        from urllib.parse import unquote
                        uuid_part, encoded_text = agentid_value.split('&', 1)
                        captcha_text = unquote(encoded_text, encoding='utf-8')
                        logger.info(f"🔍 UUID部分: {uuid_part}")
                        logger.info(f"🔍 验证码文字: {captcha_text}")
                        
                        # 清除旧的agentid并设置新的（只保留UUID部分）
                        cookies_to_remove = []
                        for cookie in self.session.cookies:
                            if cookie.name == 'agentid':
                                cookies_to_remove.append((cookie.name, cookie.domain, cookie.path))
                        
                        for name, domain, path in cookies_to_remove:
                            self.session.cookies.clear(domain, path, name)
                        
                        # 设置新的agentid（只保留UUID部分，去除汉字）
                        self.session.cookies.set('agentid', uuid_part, domain='.mail.10086.cn')
                        
                        logger.info(f"✅ 验证码获取成功，agentid: {uuid_part}, 验证码文字: {captcha_text}")
                        return True, uuid_part, captcha_text, captcha_image_base64
                    else:
                        # 如果没有&分隔符，直接使用agentid_value
                        self.session.cookies.set('agentid', agentid_value, domain='.mail.10086.cn')
                        logger.info(f"✅ 验证码获取成功，agentid: {agentid_value}")
                        return True, agentid_value, "", captcha_image_base64
                else:
                    logger.error("❌ 无法从Set-Cookie头中提取agentid")
                    return False, "无法从Set-Cookie头中提取agentid", None, None
            else:
                logger.error(f"❌ 验证码请求失败，状态码: {response.status_code}")
                return False, f"验证码请求失败，状态码: {response.status_code}", None, None
                
        except Exception as e:
            logger.error(f"❌ 获取验证码异常: {e}")
            return False, f"获取验证码异常: {e}", None, None
    
    def recognize_captcha(self, captcha_image_base64, captcha_text):
        """调用验证码识别API"""
        try:
            logger.info(f"🔍 开始识别验证码: {captcha_text}")
            
            # 验证码识别API (使用http_request_script.py中的配置)
            api_url = "https://api.geepass.cn/api/recognize/captcha"
            
            payload = {
                "token": "035909ff833446e3a906f59888080764j2gbxivgapk6czca",
                "type": 30109,
                "center": True,
                "image": captcha_image_base64,
                "ques": captcha_text
            }
            
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            logger.info(f"🔍 验证码识别请求URL: {api_url}")
            logger.info(f"🔍 验证码识别payload: {payload}")
            
            response = self.session.post(api_url, json=payload, headers=headers, verify=False, timeout=30)
            
            logger.info(f"🔍 验证码识别响应状态码: {response.status_code}")
            logger.info(f"🔍 验证码识别响应内容: {response.text}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    logger.info(f"🔍 验证码识别API完整响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    
                    # 检查响应结构 (按照http_request_script.py的逻辑)
                    if 'data' not in result:
                        logger.error("❌ 验证码识别失败: 响应中没有data字段")
                        return False, "响应中没有data字段"
                    
                    logger.info(f"🔍 result['data']的内容: {json.dumps(result['data'], ensure_ascii=False, indent=2)}")
                    
                    # API响应结构是嵌套的: data.data.targets
                    if 'data' not in result['data']:
                        logger.error("❌ 验证码识别失败: 响应data中没有data字段")
                        return False, "响应data中没有data字段"
                    
                    if 'targets' not in result['data']['data']:
                        logger.error("❌ 验证码识别失败: 响应data.data中没有targets字段")
                        return False, "响应data.data中没有targets字段"
                    
                    targets = result['data']['data']['targets']
                    logger.info(f"✅ 验证码识别成功，识别结果: {targets}")
                    return True, targets
                except json.JSONDecodeError:
                    logger.error("❌ 验证码识别响应JSON解析失败")
                    return False, "验证码识别响应JSON解析失败"
            else:
                logger.error(f"❌ 验证码识别请求失败，状态码: {response.status_code}")
                return False, f"验证码识别请求失败，状态码: {response.status_code}"
                
        except Exception as e:
            logger.error(f"❌ 验证码识别异常: {e}")
            return False, f"验证码识别异常: {e}"
    
    def submit_captcha_verification(self, agentid, verify_code, captcha_text, username):
        """提交验证码验证"""
        try:
            logger.info(f"🔍 开始提交验证码验证...")

            if not agentid:
                logger.error("❌ agentid为空")
                return False, "agentid为空"

            logger.info(f"🔍 使用的verify_code: {verify_code}")

            # 构建请求参数
            cguid = self.generate_cguid()
            randnum = self.generate_randnum()

            # 使用正确的URL格式
            url = f"https://mail.10086.cn/s?func=login:checkNewPictureCode&cguid={cguid}&randnum={randnum}"

            # 构建请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept': 'text/javascript',
                'Accept-Encoding': 'gzip, deflate, br, zstd',
                'Content-Type': 'application/xml',
                'pragma': 'no-cache',
                'cache-control': 'no-cache',
                'sec-ch-ua-platform': '"Windows"',
                'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
                'sec-ch-ua-mobile': '?0',
                'origin': 'https://mail.10086.cn',
                'sec-fetch-site': 'same-origin',
                'sec-fetch-mode': 'cors',
                'sec-fetch-dest': 'empty',
                'referer': 'https://mail.10086.cn/webmail/imgverify/index.html',
                'accept-language': 'zh-CN,zh;q=0.9',
                'priority': 'u=1, i'
            }

            # 构建XML请求体 - 使用正确的格式
            xml_body = f'''<object>
  <int name="clientId">1</int>
  <string name="verifyCode">{verify_code}</string>
  <string name="userName">{username}</string>
</object>'''

            logger.info(f"🔍 验证码验证请求URL: {url}")
            logger.info(f"🔍 验证码验证请求体: {xml_body}")

            # 发送POST请求
            response = self.session.post(url, data=xml_body, headers=headers, timeout=self.timeout)

            logger.info(f"🔍 验证码验证响应状态码: {response.status_code}")
            logger.info(f"🔍 验证码验证响应内容: {response.text}")

            if response.status_code == 200:
                logger.info("✅ 验证码验证提交成功")
                return True, "验证码验证成功"
            else:
                logger.error(f"❌ 验证码验证提交失败，状态码: {response.status_code}")
                return False, f"验证码验证提交失败，状态码: {response.status_code}"

        except Exception as e:
            logger.error(f"❌ 验证码验证提交异常: {e}")
            return False, f"验证码验证提交异常: {e}"

    def final_login_with_captcha(self, verify_code, username):
        """
        最终登录请求（验证码验证成功后）
        对应 http_request_script.py 中的 step7_final_login 函数

        Args:
            verify_code: 验证码字符串（双重编码后的）
            username: 用户名

        Returns:
            tuple: (是否成功, 消息, 重定向URL)
        """
        try:
            logger.info("🔍 开始最终登录请求（验证码验证后）")

            url = "https://mail.10086.cn/s?func=login:picLogin"

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Encoding': 'gzip, deflate, br, zstd',
                'Content-Type': 'application/x-www-form-urlencoded',
                'cache-control': 'max-age=0',
                'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'origin': 'https://mail.10086.cn',
                'upgrade-insecure-requests': '1',
                'sec-fetch-site': 'same-origin',
                'sec-fetch-mode': 'navigate',
                'sec-fetch-user': '?1',
                'sec-fetch-dest': 'document',
                'referer': 'https://mail.10086.cn/webmail/imgverify/index.html',
                'accept-language': 'zh-CN,zh;q=0.9',
                'priority': 'u=0, i'
            }

            # 构建表单数据
            form_data = {
                'VerifyCode': verify_code,
                'u': username,
                's': '1'
            }

            logger.info(f"🔍 最终登录请求URL: {url}")
            logger.info(f"🔍 最终登录表单数据: {form_data}")

            # 发送请求
            response = self.session.post(
                url,
                headers=headers,
                data=form_data,
                verify=False,  # 禁用SSL验证
                allow_redirects=False,  # 不自动跟随重定向，我们要检查302状态
                timeout=self.timeout
            )

            logger.info(f"🔍 最终登录响应状态码: {response.status_code}")
            logger.info(f"🔍 最终登录响应头: {dict(response.headers)}")
            logger.info(f"🔍 最终登录响应体: {response.text}")

            # 检查登录是否成功
            is_success = response.status_code == 302
            redirect_url = response.headers.get('location', '')

            if is_success:
                logger.info(f"✅ 最终登录成功，重定向URL: {redirect_url}")

                # 提取登录成功后的cookies
                self.extract_cookies_from_response(response)

                # 分析设置的cookies
                important_cookies = ['RMKEY', 'Os_SSo_Sid', 'Login_UserNumber', 'rmUin8881']
                for cookie in self.session.cookies:
                    if cookie.name in important_cookies:
                        logger.info(f"🔍 重要Cookie: {cookie.name}={cookie.value}")

                # 检查是否包含邮箱主页URL
                if 'appmail.mail.10086.cn' in redirect_url:
                    logger.info("✅ 登录成功！重定向到邮箱主页")
                    return True, "验证码登录成功", redirect_url
                else:
                    logger.warning("⚠️ 重定向URL异常，可能登录失败")
                    return False, "重定向URL异常，可能登录失败", redirect_url
            else:
                logger.error(f"❌ 最终登录失败，状态码: {response.status_code}")
                return False, f"最终登录失败，状态码: {response.status_code}", ""

        except Exception as e:
            logger.error(f"❌ 最终登录请求异常: {e}")
            return False, f"最终登录请求异常: {e}", ""
    
    def handle_captcha_verification(self, redirect_url, username):
        """处理验证码验证流程"""
        try:
            logger.info("🔍 开始验证码处理流程")

            # 1. 获取验证码图片
            captcha_result = self.get_captcha(redirect_url)
            if not captcha_result[0]:
                return False, f"获取验证码失败: {captcha_result[1]}"

            agentid, captcha_text, captcha_image_base64 = captcha_result[1], captcha_result[2], captcha_result[3]
            logger.info(f"✅ 获取验证码成功，agentid: {agentid}, captcha_text: {captcha_text}")

            # 2. 识别验证码
            recognize_result = self.recognize_captcha(captcha_image_base64, captcha_text)
            if not recognize_result[0]:
                return False, f"验证码识别失败: {recognize_result[1]}"

            targets = recognize_result[1]
            logger.info(f"✅ 验证码识别成功: {targets}")

            # 3. 计算坐标总和和MD5校验码
            # targets是坐标数组格式: [[x1, y1], [x2, y2], ...]
            coordinate_sum = sum(coord[0] + coord[1] for coord in targets)

            # 构建验证JSON对象
            import json
            from urllib.parse import quote

            verify_obj = {
                "k": captcha_text,
                "p": [{"x": int(coord[0]), "y": int(coord[1])} for coord in targets],
                "c": self.calculate_md5(f"{agentid}{coordinate_sum}")
            }

            # 双重URL编码
            json_str = json.dumps(verify_obj, separators=(',', ':'), ensure_ascii=False)
            first_encode = quote(json_str, safe='')
            verify_code = quote(first_encode, safe='')

            logger.info(f"🔍 坐标总和: {coordinate_sum}")
            logger.info(f"🔍 验证JSON对象: {json.dumps(verify_obj, ensure_ascii=False)}")
            logger.info(f"🔍 生成的verify_code: {verify_code}")

            # 4. 提交验证码验证
            submit_result = self.submit_captcha_verification(agentid, verify_code, captcha_text, username)
            if not submit_result[0]:
                return False, f"验证码验证提交失败: {submit_result[1]}"

            logger.info("✅ 验证码验证提交成功")

            # 5. 最终登录请求（新增步骤，对应 http_request_script.py 的 step7_final_login）
            final_login_result = self.final_login_with_captcha(verify_code, username)
            if not final_login_result[0]:
                return False, f"最终登录失败: {final_login_result[1]}"

            logger.info("✅ 验证码处理流程完成，登录成功")
            return True, "验证码处理成功，登录成功"

        except Exception as e:
            logger.error(f"❌ 验证码处理流程异常: {e}")
            return False, f"验证码处理异常: {e}"
    
    def setup_proxy(self, proxy_type, proxy_info):
        """设置代理 - 支持HTTP和SOCKS5"""
        try:
            if not proxy_type or not proxy_info:
                logger.info("ℹ️ 未提供代理信息，使用直连")
                return True
                
            proxy_type = proxy_type.lower()
            logger.info(f"🔍 设置代理: {proxy_type} - {proxy_info}")
            
            if proxy_type == 'http':
                # HTTP代理格式: *********************:port
                if not proxy_info.startswith('http://'):
                    proxy_info = 'http://' + proxy_info
                proxies = {
                    'http': proxy_info,
                    'https': proxy_info
                }
            elif proxy_type == 'socks5':
                # SOCKS5代理格式: socks5://user:pass@host:port
                if not proxy_info.startswith('socks5://'):
                    if proxy_info.startswith('socks://'):
                        proxy_info = proxy_info.replace('socks://', 'socks5://')
                    else:
                        proxy_info = 'socks5://' + proxy_info
                proxies = {
                    'http': proxy_info,
                    'https': proxy_info
                }
            else:
                logger.warning(f"⚠️ 不支持的代理类型: {proxy_type}，支持的类型: http, socks5")
                return False
            
            logger.info(f"🔍 代理配置: {proxies}")
            self.session.proxies.update(proxies)
            
            # 代理设置完成，不进行连接测试
            logger.info(f"✅ 代理设置成功: {proxy_type} - {proxy_info}")
            return True
                
        except Exception as e:
            logger.error(f"❌ 代理设置失败: {e}")
            return False
    

    

    
    def encrypt_password(self, password):
        """139邮箱密码加密 - SHA-1 + 盐值，调用统一的加密函数"""
        return self.encrypt_139_password(password)
    
    def login(self, username, password):
        """执行139邮箱登录 - 不跟随重定向，直接检测Cookie"""
        self.username = str(username)

        cguid = self.generate_cguid()
        timestamp_param = hashlib.md5(str(time.time()).encode()).hexdigest()
        encrypted_password = self.encrypt_password(password)

        url = "https://mail.10086.cn/Login/Login.ashx"
        params = {
            # 'type': 'reset',
            '_fv': '4',
            # 'cguid': "1402282369663",
            'cguid': cguid,
            '_': timestamp_param,
            # '_': "e23996f5db2a599f958b9f9ff27799abfcf0bab6",
            'resource': 'indexLogin',
            'mtime': 5
        }

        data = {
            'UserName': str(username),
            'passOld': '',
            'auto': 'on',
            'Password': encrypted_password,
            'webIndexPagePwdLogin': '1',
            'pwdType': '1',
            'clientId': '1003',
            'authType': '2'
        }

        try:
            # 使用设置的超时时间，不跟随重定向
            timeout = self.timeout if hasattr(self, 'timeout') else 10
            logger.info(f"🔍 开始登录请求，超时时间: {timeout}秒")
            logger.info(f"🔍 当前代理设置: {self.session.proxies}")
            
            response = self.session.post(url, params=params, data=data, allow_redirects=False, timeout=timeout)

            logger.info(f"🔍 登录响应状态码: {response.status_code}")
            logger.info(f"🔍 响应头Location: {response.headers.get('Location', 'None')}")

            # 直接从响应头中提取Cookie
            self.extract_cookies_from_response(response)

        except requests.exceptions.ProxyError as e:
            error_msg = f"代理连接失败: {e}"
            logger.error(f"❌ {error_msg}")
            # 检查是否是SOCKS5相关错误
            if "No such file or directory" in str(e):
                error_msg += " (可能缺少SOCKS5支持库，请检查PySocks是否正确安装)"
            elif "Connection refused" in str(e):
                error_msg += " (代理服务器拒绝连接，请检查代理地址和端口)"
            elif "Authentication failed" in str(e):
                error_msg += " (代理认证失败，请检查用户名和密码)"
            return False, error_msg
        except requests.exceptions.ConnectTimeout as e:
            error_msg = f"连接超时: {e} (请检查网络连接和代理设置)"
            logger.error(f"❌ {error_msg}")
            return False, error_msg
        except requests.exceptions.ConnectionError as e:
            error_msg = str(e)
            logger.error(f"❌ 网络连接错误: {error_msg}")
            if "Connection aborted" in error_msg:
                if "FileNotFoundError" in error_msg:
                    error_msg = "网络连接被中断，可能是代理问题: 缺少SOCKS5支持库。请确保已安装PySocks: pip install PySocks"
                else:
                    error_msg = f"网络连接被中断，可能是代理问题: {e}"
            elif "Name or service not known" in error_msg:
                error_msg = f"域名解析失败: {e} (请检查网络连接)"
            elif "Connection refused" in error_msg:
                error_msg = f"连接被拒绝: {e} (请检查代理服务器状态)"
            else:
                error_msg = f"网络连接错误: {e}"
            return False, error_msg
        except requests.exceptions.Timeout as e:
            error_msg = f"请求超时: {e} (请尝试增加超时时间或检查网络)"
            logger.error(f"❌ {error_msg}")
            return False, error_msg
        except Exception as e:
            error_msg = f"网络请求失败: {e}"
            logger.error(f"❌ {error_msg}")
            return False, error_msg

        # 检查是否获取到Cookie
        logger.info(f"🔍 提取的Os_SSo_Sid: {self.os_sso_sid}")
        logger.info(f"🔍 提取的RMKEY: {self.rmkey}")
        logger.info(f"🔍 当前Cookie数量: {len(list(self.session.cookies))}")

        # 打印所有Cookie
        for cookie in self.session.cookies:
            logger.info(f"🔍 Cookie: {cookie.name}={cookie.value}")

        # 检查响应状态码和Cookie
        if response.status_code in [200, 302]:
            # 无论是200还是302，只要有Cookie就认为可能成功
            cookie_string = self.get_cookies_string()
            if cookie_string:
                logger.info(f"✅ 获取到Cookie: {cookie_string}")

                # 检查是否有关键Cookie，特别是Os_SSo_Sid
                if self.os_sso_sid:
                    logger.info("✅ 获取到关键Cookie（Os_SSo_Sid），登录成功")
                    return True, "登录成功"
                else:
                    logger.warning("❌ 未获取到Os_SSo_Sid，检查302重定向Location")
                    # 检查302重定向的Location URL中的ec参数
                    redirect_url = response.headers.get('Location', '')
                    logger.info(f"🔍 重定向URL: {redirect_url}")
                    
                    if redirect_url:
                        # 解析URL参数
                        from urllib.parse import urlparse, parse_qs
                        parsed_url = urlparse(redirect_url)
                        query_params = parse_qs(parsed_url.query)
                        ec_value = query_params.get('ec', [None])[0]
                        
                        logger.info(f"🔍 解析到的ec参数: {ec_value}")
                        
                        if ec_value == 'MW0016':
                            logger.warning("📱 检测到ec=MW0016，触发手机验证码")
                            return False, "登录失败:触发手机验证码"
                        elif ec_value == 'S001':
                            logger.error("❌ 检测到ec=S001，验证码错误")
                            return False, "登录失败:验证码错误"
                        elif ec_value == 'S002' or ec_value == 'S004':
                            logger.error("❌ 检测到ec=S002/S004，账号不存在")
                            return False, "登录失败:账号不存在，立即注册"
                        elif ec_value == 'S003':
                            logger.error("❌ 检测到ec=S003，账号已注销")
                            return False, "登录失败:账号已注销，请重新注册"
                        elif ec_value == 'S006':
                            logger.error("❌ 检测到ec=S006，登录错误次数过多")
                            return False, "登录失败:登录操作错误次数达到了系统限制"
                        elif ec_value == 'S010':
                            logger.error("❌ 检测到ec=S010，账号被冻结")
                            return False, "登录失败:您的139邮箱账号被暂时冻结"
                        elif ec_value == 'S011':
                            logger.warning("📱 检测到ec=S011，短信验证码图片验证码")
                            return False, "登录失败:需要短信验证码的图片验证码"
                        elif ec_value == 'S035':
                            logger.warning("🔒 检测到ec=S035，长时间未修改密码")
                            return False, "登录失败:由于您长时间未修改密码，为了您的邮箱账户安全，请使用短信登录或修改密码"
                        elif ec_value == 'S056':
                            logger.warning("🔐 检测到ec=S056，需要二次验证")
                            return False, "登录失败:二次验证或者账密短信安全验证"
                        elif ec_value == 'S053':
                            logger.warning("🔐 检测到ec=S053，需要二次验证")
                            return False, "登录失败:系统检测到您的账号存在安全风险，请您使用短信验证登录验证身份"
                        elif ec_value == 'S203':
                            logger.error("❌ 检测到ec=S203，网络或系统原因")
                            return False, "登录失败:由于网络或系统原因，暂时无法登录"
                        elif ec_value == 'MW0005':
                            logger.warning("📱 检测到ec=MW0005，短信验证码图片验证码")
                            return False, "登录失败:需要短信验证码的图片验证码"
                        elif ec_value == 'MW0008':
                            logger.warning("📱 检测到ec=MW0008，短信验证码图片验证码")
                            return False, "登录失败:需要短信验证码的图片验证码"
                        elif ec_value == 'MW0009':
                            logger.warning("📱 检测到ec=MW0009，短信验证码图片验证码")
                            return False, "登录失败:需要短信验证码的图片验证码"
                        elif ec_value == 'S025':
                            logger.warning("🔍 检测到ec=S025，需要验证码验证")
                            # S025表示需要验证码验证，进入验证码处理流程
                            captcha_result = self.handle_captcha_verification(redirect_url, username)
                            if captcha_result[0]:  # 验证码处理成功
                                logger.info("✅ 验证码处理成功，重新尝试登录")
                                # 验证码处理成功后，重新尝试登录
                                return self.login(username, password)
                            else:
                                logger.error(f"❌ 验证码处理失败: {captcha_result[1]}")
                                return False, f"验证码处理失败: {captcha_result[1]}"
                        elif ec_value == 'PML401010062':
                            logger.warning("🔐 检测到ec=PML401010062，非信任浏览器")
                            return False, "登录失败:当前不是您的信任登录浏览器，为了您的邮箱账户安全，请进行短信验证"
                        else:
                            logger.error(f"❌ Os_SSo_Sid获取失败，ec参数: {ec_value}")
                            if ec_value:
                                return False, f"登录失败:未知错误ec={ec_value}"
                            else:
                                return False, "登录失败:账号或密码错误"
                    else:
                        if ec_value:
                            return False, f"登录失败:未知错误ec={ec_value}"
                        else:
                            return False, "登录失败:账号或密码错误"
            else:
                redirect_url = response.headers.get('Location', '')
                logger.info(f"🔍 无Cookie时的重定向URL: {redirect_url}")
                
                if redirect_url:
                    # 解析URL参数检查ec值
                    from urllib.parse import urlparse, parse_qs
                    parsed_url = urlparse(redirect_url)
                    query_params = parse_qs(parsed_url.query)
                    ec_value = query_params.get('ec', [None])[0]
                    
                    logger.info(f"🔍 无Cookie时解析到的ec参数: {ec_value}")
                    
                    if ec_value == 'MW0016':
                        logger.warning("📱 检测到ec=MW0016，触发手机验证码")
                        return False, "登录失败:触发手机验证码"
                    elif ec_value == 'S001':
                        logger.error("❌ 检测到ec=S001，验证码错误")
                        return False, "登录失败:验证码错误"
                    elif ec_value == 'S002' or ec_value == 'S004':
                        logger.error("❌ 检测到ec=S002/S004，账号不存在")
                        return False, "登录失败:账号不存在，立即注册"
                    elif ec_value == 'S003':
                        logger.error("❌ 检测到ec=S003，账号已注销")
                        return False, "登录失败:账号已注销，请重新注册"
                    elif ec_value == 'S006':
                        logger.error("❌ 检测到ec=S006，登录错误次数过多")
                        return False, "登录失败:登录操作错误次数达到了系统限制"
                    elif ec_value == 'S010':
                        logger.error("❌ 检测到ec=S010，账号被冻结")
                        return False, "登录失败:您的139邮箱账号被暂时冻结"
                    elif ec_value == 'S011':
                        logger.warning("📱 检测到ec=S011，短信验证码图片验证码")
                        return False, "登录失败:需要短信验证码的图片验证码"
                    elif ec_value == 'S035':
                        logger.warning("🔒 检测到ec=S035，长时间未修改密码")
                        return False, "登录失败:由于您长时间未修改密码，为了您的邮箱账户安全，请使用短信登录或修改密码"
                    elif ec_value == 'S056':
                        logger.warning("🔐 检测到ec=S056，需要二次验证")
                        return False, "登录失败:二次验证或者账密短信安全验证"
                    elif ec_value == 'S203':
                        logger.error("❌ 检测到ec=S203，网络或系统原因")
                        return False, "登录失败:由于网络或系统原因，暂时无法登录"
                    elif ec_value == 'MW0005':
                        logger.warning("📱 检测到ec=MW0005，短信验证码图片验证码")
                        return False, "登录失败:需要短信验证码的图片验证码"
                    elif ec_value == 'MW0008':
                        logger.warning("📱 检测到ec=MW0008，短信验证码图片验证码")
                        return False, "登录失败:需要短信验证码的图片验证码"
                    elif ec_value == 'MW0009':
                        logger.warning("📱 检测到ec=MW0009，短信验证码图片验证码")
                        return False, "登录失败:需要短信验证码的图片验证码"
                    elif ec_value == 'S025':
                        logger.warning("🔍 检测到ec=S025，需要验证码验证")
                        # S025表示需要验证码验证，进入验证码处理流程
                        captcha_result = self.handle_captcha_verification(redirect_url, username)
                        if captcha_result[0]:  # 验证码处理成功
                            logger.info("✅ 验证码处理成功，重新尝试登录")
                            # 验证码处理成功后，重新尝试登录
                            return self.login(username, password)
                        else:
                            logger.error(f"❌ 验证码处理失败: {captcha_result[1]}")
                            return False, f"验证码处理失败: {captcha_result[1]}"
                    elif ec_value == 'PML401010062':
                        logger.warning("🔐 检测到ec=PML401010062，非信任浏览器")
                        return False, "登录失败:当前不是您的信任登录浏览器，为了您的邮箱账户安全，请进行短信验证"
                    elif 'imgverify' in redirect_url or 'verify' in redirect_url:
                        logger.warning("🔍 检测到需要验证码，开始验证码处理流程")
                        # 开始验证码处理流程
                        captcha_result = self.handle_captcha_verification(redirect_url, username)
                        if captcha_result[0]:  # 验证码处理成功
                            logger.info("✅ 验证码处理成功，重新尝试登录")
                            # 验证码处理成功后，重新尝试登录
                            return self.login(username, password)
                        else:
                            logger.error(f"❌ 验证码处理失败: {captcha_result[1]}")
                            return False, f"验证码处理失败: {captcha_result[1]}"
                    else:
                        if ec_value:
                            return False, f"登录失败:未知错误ec={ec_value}"
                        else:
                            return False, "登录失败:账号或密码错误"
                else:
                    return False, "登录失败:账号或密码错误"
        else:
            return False, "登录失败:账号或密码错误"
    
    def extract_cookies_from_response(self, response):
        """直接从响应头中提取Cookie"""
        set_cookie_headers = response.headers.get_list('Set-Cookie') if hasattr(response.headers, 'get_list') else []
        if not set_cookie_headers:
            # 如果没有get_list方法，尝试获取单个Set-Cookie头
            set_cookie_header = response.headers.get('Set-Cookie')
            if set_cookie_header:
                set_cookie_headers = [set_cookie_header]

        logger.info(f"🔍 Set-Cookie头数量: {len(set_cookie_headers)}")

        for cookie_header in set_cookie_headers:
            logger.info(f"🔍 Set-Cookie: {cookie_header}")
            # 解析Cookie
            cookie_parts = cookie_header.split(';')[0]  # 只取第一部分
            if '=' in cookie_parts:
                name, value = cookie_parts.split('=', 1)
                name = name.strip()
                value = value.strip()

                # 设置到session中
                self.session.cookies.set(name, value)
                logger.info(f"🔍 设置Cookie: {name}={value}")

        # 提取会话信息
        self.extract_session_info()

    def extract_session_info(self):
        """从Cookie中提取会话信息"""
        for cookie in self.session.cookies:
            if cookie.name == 'Os_SSo_Sid':
                self.os_sso_sid = cookie.value
            elif cookie.name == 'RMKEY':
                self.rmkey = cookie.value
    
    def get_cookies_string(self):
        """获取Cookie字符串格式"""
        cookies_list = []
        for cookie in self.session.cookies:
            cookies_list.append(f"{cookie.name}={cookie.value}")
        return "; ".join(cookies_list)
    
    def set_cookies_from_string(self, cookie_string):
        """从Cookie字符串设置会话Cookie"""
        try:
            self.session.cookies.clear()
            if cookie_string:
                for cookie_pair in cookie_string.split('; '):
                    if '=' in cookie_pair:
                        name, value = cookie_pair.split('=', 1)
                        self.session.cookies.set(name.strip(), value.strip())
                
                # 重新提取会话信息
                self.extract_session_info()
            return True
        except Exception as e:
            logger.error(f"❌ Cookie设置失败: {e}")
            return False
    
    def generate_random_content(self):
        """生成随机邮件内容"""
        # 随机主题列表（避免敏感词）
        subjects = [
            "工作汇报", "项目进展", "会议纪要", "资料分享", "技术交流",
            "学习心得", "经验总结", "问题讨论", "方案建议", "数据统计"
        ]
        
        # 随机内容模板
        content_templates = [
            "您好！\n\n这是关于{}的相关信息，请查收。\n\n如有疑问，请随时联系。\n\n谢谢！",
            "Hi，\n\n分享一些{}的内容给您参考。\n\n希望对您有帮助。\n\n祝好！",
            "您好，\n\n附上{}的详细资料。\n\n请您查阅，如需进一步讨论请告知。\n\n此致\n敬礼！",
            "Hello，\n\n关于{}的事项，现汇报如下。\n\n请您审阅。\n\n谢谢！",
            "您好！\n\n{}的相关工作已完成，请查看。\n\n如有问题请及时反馈。\n\n谢谢！"
        ]
        
        # 随机选择
        subject = random.choice(subjects)
        content_template = random.choice(content_templates)
        content = content_template.format(subject)
        
        # 添加随机数字避免重复
        random_num = random.randint(1000, 9999)
        subject_with_num = f"{subject}_{random_num}"
        
        return subject_with_num, content

    def send_email(self, to_email, subject, content, cc_email="", bcc_email="", is_html=1):
        """发送邮件"""
        logger.info("=" * 40)
        logger.info("📤 开始邮件发送流程")
        
        if not self.os_sso_sid:
            logger.error("❌ 缺少Os_SSo_Sid，请先登录")
            return False, "缺少Os_SSo_Sid，请先登录"
        
        logger.info(f"✅ Os_SSo_Sid验证通过: {self.os_sso_sid[:20]}...")
        logger.info(f"📧 收件人: {to_email}")
        logger.info(f"📧 主题: {subject}")
        logger.info(f"📧 内容长度: {len(content)}")
        logger.info(f"📧 抄送: {cc_email}")
        logger.info(f"📧 密送: {bcc_email}")
        logger.info(f"📧 HTML格式: {is_html}")

        cguid = self.generate_cguid()
        stime = str(int(time.time() * 1000))

        # 生成guid (简单的随机字符串)
        guid = ''.join(random.choices('0123456789abcdef', k=15))

        logger.info(f"🔧 生成参数 - cguid: {cguid}, stime: {stime}, guid: {guid}")

        url = "https://appmail.mail.10086.cn/RmWeb/mail"
        params = {
            'func': 'mbox:compose',
            'categroyId': '103000000',
            'os_sso_sid': self.os_sso_sid,
            'comefrom': '54',
            'guid': guid,
            'stime': stime,
            'cguid': cguid
        }

        logger.info(f"🌐 请求URL: {url}")
        logger.info(f"📝 请求参数: {params}")

        # 处理邮件内容，将换行符转换为HTML格式
        def format_content_to_html(text_content):
            """将文本内容转换为HTML格式，处理换行符"""
            if not text_content:
                return ""
            
            # 首先将字面的\n字符串替换为实际的换行符
            # 这样可以处理base64解码后包含\n字符串的情况
            text_content = text_content.replace('\\n', '\n')
            
            # 将换行符\n转换为HTML的<br>标签
            lines = text_content.split('\n')
            html_lines = []
            
            for line in lines:
                # 每一行都包装在div中，并使用微软雅黑字体
                if line.strip():  # 非空行
                    html_lines.append(f'<div style="line-height: 1.5; overflow-wrap: break-word; word-break: break-word;"><font face="微软雅黑">{line}</font></div>')
                else:  # 空行，添加一个空的div来保持换行效果
                    html_lines.append('<div style="line-height: 1.5; overflow-wrap: break-word; word-break: break-word;"><font face="微软雅黑"><br></font></div>')
            
            # 添加签名容器
            signature_container = '<div id="signContainer"><table id="signTable" style="width: 100%; white-space: initial;"><tbody></tbody></table><style contenteditable="false">#signContainer{word-wrap:break-word;word-break:break-all;}#signContainer p{margin:0;}</style></div>'
            
            return ''.join(html_lines) + signature_container

        # 使用传入的content参数生成HTML内容
        content_raw = format_content_to_html(content)
        
        logger.info(f"\n📝 原始文本内容: {content}")
        logger.info(f"\n🎨 转换后的HTML内容: {content_raw}")

        # 进行XML转义
        content_escaped = html.escape(content_raw)

        logger.info(f"\n🔒 XML转义后内容: {content_escaped[:200]}...")
        
        # 对主题也进行XML转义
        subject_escaped = html.escape(subject) if subject else ""
        logger.info(f"\n📧 原始主题: {subject}")
        logger.info(f"\n🔒 XML转义后主题: {subject_escaped}")
        
        # 构造XML请求体
        xml_data = f'''<object>
          <object name="attrs">
            <string name="id" />
            <string name="mid" />
            <string name="messageId" />
            <string name="account">{self.username}@139.com</string>
            <string name="to">{to_email}</string>
            <string name="cc">{cc_email}</string>
            <string name="bcc">{bcc_email}</string>
            <int name="showOneRcpt">0</int>
            <int name="isHtml">{is_html}</int>
            <string name="subject">{subject_escaped}</string>
            <string name="content">{content_escaped}</string>
            <int name="priority">3</int>
            <int name="signatureId">0</int>
            <int name="stationeryId">0</int>
            <int name="saveSentCopy">1</int>
            <int name="requestReadReceipt">0</int>
            <int name="inlineResources">1</int>
            <int name="scheduleDate">0</int>
            <int name="normalizeRfc822">0</int>
            <int name="busAssistant">0</int>
            <int name="vipAssistant">0</int>
            <array name="attachments">
            </array>
            <object name="headers">
            </object>
            <string name="atRemind" />
            <int name="isDeliverStatus">0</int>
            <null name="denyForward" />
          </object>
          <string name="action">asyn</string>
          <int name="replyNotify">0</int>
          <int name="returnInfo">1</int>
        </object>'''
        # xml_data = "<object>\n  <object name=\"attrs\">\n    <string name=\"id\"></string>\n    <string name=\"mid\" />\n    <string name=\"messageId\" />\n    <string name=\"account\"><EMAIL></string>\n    <string name=\"to\"><EMAIL></string>\n    <string name=\"cc\" />\n    <string name=\"bcc\" />\n    <int name=\"showOneRcpt\">0</int>\n    <int name=\"isHtml\">1</int>\n    <string name=\"subject\">你好,悠然,这是一封测试换行信</string>\n    <string name=\"content\">&lt;div style=&quot;line-height: 1.5; overflow-wrap: break-word; word-break: break-word;&quot;&gt;&lt;font face=&quot;微软雅黑&quot;&gt;你好,悠然,这是一封测试换行信&lt;/font&gt;&lt;/div&gt;&lt;div style=&quot;line-height: 1.5; overflow-wrap: break-word; word-break: break-word;&quot;&gt;&lt;font face=&quot;微软雅黑&quot;&gt;嘿嘿&lt;/font&gt;&lt;/div&gt;&lt;div style=&quot;line-height: 1.5; overflow-wrap: break-word; word-break: break-word;&quot;&gt;&lt;font face=&quot;微软雅黑&quot;&gt;哈哈&lt;br&gt;&lt;/font&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;/div&gt;&lt;div id=&quot;signContainer&quot;&gt;&lt;table id=&quot;signTable&quot; style=&quot;width: 100%; white-space: initial;&quot;&gt;&lt;tbody&gt;&lt;/tbody&gt;&lt;/table&gt;&lt;style contenteditable=&quot;false&quot;&gt;#signContainer{word-wrap:break-word;word-break:break-all;}#signContainer p{margin:0;}&lt;/style&gt;&lt;/div&gt;</string>\n    <int name=\"priority\">3</int>\n    <int name=\"signatureId\">0</int>\n    <int name=\"stationeryId\">0</int>\n    <int name=\"saveSentCopy\">1</int>\n    <int name=\"requestReadReceipt\">0</int>\n    <int name=\"inlineResources\">1</int>\n    <int name=\"scheduleDate\">0</int>\n    <int name=\"normalizeRfc822\">0</int>\n    <int name=\"busAssistant\">0</int>\n    <int name=\"vipAssistant\">0</int>\n    <array name=\"attachments\">\n    </array>\n    <object name=\"headers\">\n    </object>\n    <string name=\"atRemind\" />\n    <int name=\"isDeliverStatus\">0</int>\n    <null name=\"denyForward\" />\n  </object>\n  <string name=\"action\">asyn</string>\n  <int name=\"replyNotify\">0</int>\n  <int name=\"returnInfo\">1</int>\n</object>"

        logger.info(f"📄 XML请求体长度: {len(xml_data)}")

        headers = {
            'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
          'Accept-Encoding': "gzip, deflate, br, zstd",
          'Content-Type': "application/xml",
          'sec-ch-ua-platform': "\"Windows\"",
          'sec-ch-ua': "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
          'sec-ch-ua-mobile': "?0",
          'origin': "https://appmail10.mail.10086.cn",
          'sec-fetch-site': "same-origin",
          'sec-fetch-mode': "cors",
          'sec-fetch-dest': "empty",
          'accept-language': "zh-CN,zh;q=0.9",
          'priority': "u=1, i"
        }

        logger.info(f"📋 请求头: {headers}")

        try:
            # 使用设置的超时时间
            timeout = self.timeout if hasattr(self, 'timeout') else 10
            logger.info(f"⏱️ 请求超时时间: {timeout}秒")
            logger.info(f"🌐 当前代理设置: {self.session.proxies}")
            
            logger.info("🚀 发送HTTP请求...")
            response = self.session.post(url, params=params, data=xml_data, headers=headers, timeout=timeout)

            logger.info(f"📥 响应状态码: {response.status_code}")
            logger.info(f"📥 响应头: {dict(response.headers)}")

            if response.status_code == 200:
                response_text = response.text
                logger.info(f"📥 响应内容长度: {len(response_text)}")
                logger.info(f"📥 响应内容前500字符: {response_text[:500]}")

                # 简单检查是否成功
                if "'code':'S_OK'" in response_text and "'tid':" in response_text:
                    # 提取tid
                    import re
                    tid_match = re.search(r"'tid':'([^']+)'", response_text)
                    if tid_match:
                        tid = tid_match.group(1)
                        success_msg = f"邮件发送成功，任务ID: {tid}"
                        logger.info(f"✅ {success_msg}")
                        return True, success_msg

                # 邮件发送失败，尝试解析具体错误原因
                logger.error(f"❌ 邮件发送失败，开始解析错误原因...")
                logger.info(f"📝 完整服务器响应: {response_text}")
                
                # 尝试从响应中提取错误代码和消息
                import re
                error_details = []
                
                # 提取错误代码
                code_match = re.search(r"'code':'([^']+)'", response_text)
                if code_match:
                    error_code = code_match.group(1)
                    error_details.append(f"错误代码: {error_code}")
                    logger.error(f"🔍 找到错误代码: {error_code}")
                
                # 提取错误消息
                msg_match = re.search(r"'msg':'([^']+)'", response_text)
                if msg_match:
                    error_msg = msg_match.group(1)
                    error_details.append(f"错误消息: {error_msg}")
                    logger.error(f"🔍 找到错误消息: {error_msg}")
                
                # 检查常见错误模式
                if "'code':'S305'" in response_text:
                    error_details.append("移动认证登录失败")
                    logger.error("🔍 检测到S305错误: 移动认证登录失败")
                elif "'code':'S052'" in response_text:
                    error_details.append("系统繁忙")
                    logger.error("🔍 检测到S052错误: 系统繁忙")
                elif "'code':'S053'" in response_text:
                    error_details.append("账号存在安全风险")
                    logger.error("🔍 检测到S053错误: 账号存在安全风险")
                elif "PML" in response_text:
                    # 提取PML错误码
                    pml_match = re.search(r"'code':'(PML[^']+)'", response_text)
                    if pml_match:
                        pml_code = pml_match.group(1)
                        error_details.append(f"PML错误码: {pml_code}")
                        logger.error(f"🔍 检测到PML错误: {pml_code}")
                
                # 构造详细的错误信息
                if error_details:
                    detailed_error = "邮件发送失败 - " + ", ".join(error_details)
                else:
                    detailed_error = f"邮件发送失败 - 未知错误，响应内容: {response_text[:200]}..."
                
                logger.error(f"📝 最终错误信息: {detailed_error}")
                return False, detailed_error
            else:
                error_msg = f"请求失败，状态码: {response.status_code}"
                logger.error(f"❌ HTTP请求失败: {error_msg}")
                try:
                    response_text = response.text
                    logger.info(f"📝 错误响应内容: {response_text[:200]}...")
                except:
                    logger.warning("📝 无法获取响应内容")
                return False, error_msg

        except Exception as e:
            error_msg = f"网络请求异常: {str(e)}"
            logger.error(f"❌ 网络异常: {error_msg}")
            import traceback
            logger.info(f"📝 异常堆栈: {traceback.format_exc()}")
            return False, error_msg

# API路由定义

@ns_auth.route('/login')
class LoginAPI(Resource):
    @ns_auth.doc('login')
    @ns_auth.expect(encrypted_request_model)
    @ns_auth.response(200, '登录成功', encrypted_response_model)
    @ns_auth.response(400, '请求参数错误', encrypted_response_model)
    @ns_auth.response(401, '登录失败', encrypted_response_model)
    def post(self):
        """用户登录接口（AES加密版本）

        🔐 加密传输说明:
        - 请求格式: {"data": "AES加密后的Base64字符串"}
        - 响应格式: {"data": "AES加密后的Base64字符串"}
        
        📝 原始数据格式（加密前）:
        - 请求: {"username": "13967085627", "password": "your_password", "proxy_type": "socks5", "proxy_info": "127.0.0.1:1080"}
        - 响应: {"username": "13967085627", "cookie": "cookie_string", "msg": "登录成功"}
        
        🔑 加密参数:
        - 算法: AES-256-CBC
        - 密钥: 固定32字节密钥
        - 填充: PKCS7
        - 编码: Base64
        """
        try:
            data = request.get_json()
            if not data:
                error_response = {"msg": "请求参数格式错误，需要JSON格式"}
                return create_encrypted_response(error_response), 400

            encrypted_data = data.get('data')
            if not encrypted_data:
                error_response = {"msg": "加密数据不能为空"}
                return create_encrypted_response(error_response), 400

            # 解密请求数据
            login_data, error_msg = decrypt_request_data(encrypted_data)
            if not login_data:
                error_response = {"msg": error_msg}
                return create_encrypted_response(error_response), 400

            username = login_data.get('username')
            password = login_data.get('password')
            proxy_type = login_data.get('proxy_type')
            proxy_info = login_data.get('proxy_info')

            if not username or not password:
                error_response = {"msg": "用户名和密码不能为空"}
                return create_encrypted_response(error_response), 400

            # 创建邮件客户端（支持代理）
            client = Mail139Client(proxy_type, proxy_info)
            success, message = client.login(username, password)

            if success:
                # 登录成功，返回Cookie字符串
                cookie_string = client.get_cookies_string()
                response_data = {
                    'username': username,
                    'cookie': cookie_string,
                    'msg': '登录成功'
                }
                return create_encrypted_response(response_data)
            else:
                # 登录失败，返回具体原因
                error_response = {"msg": message}
                return create_encrypted_response(error_response), 401

        except Exception as e:
            error_response = {"msg": f"服务器内部错误: {str(e)}"}
            return create_encrypted_response(error_response), 500

@ns_mail.route('/send')
class SendEmailAPI(Resource):
    @ns_mail.doc('send_email')
    @ns_mail.expect(encrypted_request_model)
    @ns_mail.response(200, '发送成功', encrypted_response_model)
    @ns_mail.response(400, '发送失败', encrypted_response_model)
    @ns_mail.response(401, '认证失败', encrypted_response_model)
    def post(self):
        """发送邮件接口（AES加密版本）

        🔐 加密传输说明:
        - 请求格式: {"data": "AES加密后的Base64字符串"}
        - 响应格式: {"data": "AES加密后的Base64字符串"}
        
        📝 原始数据格式（加密前）:
        - 请求: {"username": "13967085627", "cookie": "cookie_string", "recipient": "<EMAIL>", "subject": "base64编码的主题", "content": "base64编码的内容", "proxy_type": "socks5", "proxy_info": "127.0.0.1:1080"}
        - 响应: {"recipient": "<EMAIL>", "msg": "发信成功"}
        
        📧 邮件内容编码说明:
        - subject: 邮件主题需要进行base64编码传输
        - content: 邮件内容需要进行base64编码传输
        - 服务端会自动解码base64字符串为UTF-8文本
        
        🔑 加密参数:
        - 算法: AES-256-CBC
        - 密钥: 固定32字节密钥
        - 填充: PKCS7
        - 编码: Base64
        """
        try:
            logger.info("=" * 50)
            logger.info("📧 开始处理邮件发送请求")
            
            data = request.get_json()
            if not data:
                logger.error("❌ 请求参数格式错误，需要JSON格式")
                error_response = {"msg": "请求参数格式错误，需要JSON格式"}
                return create_encrypted_response(error_response), 400

            encrypted_data = data.get('data')
            if not encrypted_data:
                logger.error("❌ 加密数据不能为空")
                error_response = {"msg": "加密数据不能为空"}
                return create_encrypted_response(error_response), 400

            logger.info(f"🔐 收到加密数据长度: {len(encrypted_data)}")

            # 解密请求数据
            email_data, error_msg = decrypt_request_data(encrypted_data)
            if not email_data:
                logger.error(f"❌ 数据解密失败: {error_msg}")
                error_response = {"msg": error_msg}
                return create_encrypted_response(error_response), 400

            logger.info("✅ 数据解密成功")
            logger.info(f"📝 解密后的数据字段: {list(email_data.keys())}")

            username = email_data.get('username')
            cookie = email_data.get('cookie')
            proxy_type = email_data.get('proxy_type')
            proxy_info = email_data.get('proxy_info')
            subject = email_data.get('subject')
            recipient = email_data.get('recipient')
            content = email_data.get('content')

            logger.info(f"👤 用户名: {username}")
            logger.info(f"📧 收件人: {recipient}")
            logger.info(f"🍪 Cookie长度: {len(cookie) if cookie else 0}")
            logger.info(f"🌐 代理类型: {proxy_type}")
            logger.info(f"🌐 代理信息: {proxy_info}")

            if not username or not cookie:
                logger.error("❌ 用户名和Cookie不能为空")
                error_response = {"msg": "用户名和Cookie不能为空"}
                return create_encrypted_response(error_response), 400

            if not recipient:
                logger.error("❌ 收件人邮箱不能为空")
                error_response = {"msg": "收件人邮箱不能为空"}
                return create_encrypted_response(error_response), 400

            # 创建邮件客户端（支持代理）
            logger.info("🔧 创建邮件客户端...")
            client = Mail139Client(proxy_type, proxy_info)
            client.username = username

            # 设置Cookie
            logger.info("🍪 设置Cookie...")
            if not client.set_cookies_from_string(cookie):
                logger.error("❌ Cookie格式无效")
                error_response = {"msg": "Cookie格式无效"}
                return create_encrypted_response(error_response), 401

            logger.info("✅ Cookie设置成功")

            # 对邮件主题和内容进行base64解码
            import base64
            
            # 解码主题
            if subject:
                try:
                    logger.info(f"📧 原始主题(base64): {subject}")
                    subject_decoded = base64.b64decode(subject).decode('utf-8')
                    logger.info(f"📧 解码后主题: {subject_decoded}")
                    subject = subject_decoded
                except Exception as e:
                    logger.warning(f"⚠️ 主题base64解码失败，使用原始值: {str(e)}")
                    logger.info(f"📧 邮件主题(原始): {subject}")
            
            # 解码内容
            if content:
                try:
                    logger.info(f"📧 原始内容(base64): {content[:100]}...")
                    content_decoded = base64.b64decode(content).decode('utf-8')
                    logger.info(f"📧 解码后内容: {content_decoded[:100]}...")
                    content = content_decoded
                except Exception as e:
                    logger.warning(f"⚠️ 内容base64解码失败，使用原始值: {str(e)}")
                    logger.info(f"📧 邮件内容(原始): {content[:100]}...")

            # 如果没有提供主题和内容，生成随机内容
            if not subject or not content:
                logger.info("📧 生成随机邮件内容...")
                subject, content = client.generate_random_content()
                logger.info(f"📧 随机主题: {subject}")
                logger.info(f"📧 随机内容: {content[:100]}...")

            # 发送邮件
            logger.info("📤 开始发送邮件...")
            success, message = client.send_email(recipient, subject, content)

            logger.info(f"📤 邮件发送结果: {'成功' if success else '失败'}")
            logger.info(f"📝 返回消息: {message}")

            if success:
                response_data = {
                    'recipient': recipient,
                    'msg': '发信成功'
                }
                logger.info("✅ 邮件发送成功，返回成功响应")
                return create_encrypted_response(response_data)
            else:
                logger.error(f"❌ 邮件发送失败: {message}")
                error_response = {"msg": message}
                return create_encrypted_response(error_response), 400

        except Exception as e:
            logger.error(f"❌ 服务器内部错误: {str(e)}")
            import traceback
            logger.error(f"📝 错误堆栈: {traceback.format_exc()}")
            error_response = {"msg": f"服务器内部错误: {str(e)}"}
            return create_encrypted_response(error_response), 500

def start_server():
    """启动服务器"""
    logger.info("=" * 60)
    logger.info("139邮箱API服务启动中...")
    logger.info("=" * 60)
    logger.info("可用接口:")
    logger.info("  POST /auth/login                - 用户登录（AES加密版本）")
    logger.info("  POST /mail/send                 - 发送邮件（AES加密版本）")

    logger.info("")
    logger.info("🔐 AES加密传输说明:")
    logger.info("  - POST接口请求格式: {\"data\": \"AES加密后的Base64字符串\"}")
    logger.info("  - 所有接口响应格式: {\"data\": \"AES加密后的Base64字符串\"}")
    logger.info("  - 加密算法: AES-256-ECB")
    logger.info("  - 密钥长度: 32字节（固定密钥）")
    logger.info("  - 填充方式: PKCS7")
    logger.info("  - 编码格式: Base64")
    logger.info("  - 模式特性: 相同明文产生相同密文（无需IV）")
    logger.info("")
    logger.info("📖 使用示例:")
    logger.info("  1. 将原始JSON数据进行AES加密")
    logger.info("  2. POST请求: {\"data\": \"加密后的Base64字符串\"}")
    logger.info("  3. 响应: {\"data\": \"加密后的Base64字符串\"}")
    logger.info("  4. 解密响应中的data字段获得原始JSON数据")
    logger.info("")
    logger.info("服务地址: http://localhost:5000")
    logger.info("Swagger文档: http://localhost:5000/docs/")
    logger.info("=" * 60)
    
    # 无窗口运行，关闭debug模式
    app.run(host='0.0.0.0', port=5000, debug=False, use_reloader=False)

if __name__ == '__main__':
    import sys
    import os
    
    # 检查是否是打包后的exe运行
    if getattr(sys, 'frozen', False):
        # 如果是exe运行，隐藏控制台窗口
        import ctypes
        ctypes.windll.user32.ShowWindow(ctypes.windll.kernel32.GetConsoleWindow(), 0)
    
    start_server()
