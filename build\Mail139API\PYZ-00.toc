('C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\build\\Mail139API\\PYZ-00.pyz',
 [('__future__',
   'C:\\Program Files\\Python310\\lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Program Files\\Python310\\lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'C:\\Program Files\\Python310\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Program Files\\Python310\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Program Files\\Python310\\lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc', 'C:\\Program Files\\Python310\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal',
   'C:\\Program Files\\Python310\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('_strptime', 'C:\\Program Files\\Python310\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Program Files\\Python310\\lib\\_threading_local.py',
   'PYMODULE'),
  ('aniso8601',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\aniso8601\\__init__.py',
   'PYMODULE'),
  ('aniso8601.builders',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\aniso8601\\builders\\__init__.py',
   'PYMODULE'),
  ('aniso8601.builders.python',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\aniso8601\\builders\\python.py',
   'PYMODULE'),
  ('aniso8601.compat',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\aniso8601\\compat.py',
   'PYMODULE'),
  ('aniso8601.date',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\aniso8601\\date.py',
   'PYMODULE'),
  ('aniso8601.decimalfraction',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\aniso8601\\decimalfraction.py',
   'PYMODULE'),
  ('aniso8601.duration',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\aniso8601\\duration.py',
   'PYMODULE'),
  ('aniso8601.exceptions',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\aniso8601\\exceptions.py',
   'PYMODULE'),
  ('aniso8601.interval',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\aniso8601\\interval.py',
   'PYMODULE'),
  ('aniso8601.resolution',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\aniso8601\\resolution.py',
   'PYMODULE'),
  ('aniso8601.time',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\aniso8601\\time.py',
   'PYMODULE'),
  ('aniso8601.timezone',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\aniso8601\\timezone.py',
   'PYMODULE'),
  ('aniso8601.utcoffset',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\aniso8601\\utcoffset.py',
   'PYMODULE'),
  ('argparse', 'C:\\Program Files\\Python310\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\Program Files\\Python310\\lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'C:\\Program Files\\Python310\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Program Files\\Python310\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Program Files\\Python310\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Program Files\\Python310\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Program Files\\Python310\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Program Files\\Python310\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Program Files\\Python310\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Program Files\\Python310\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Program Files\\Python310\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Program Files\\Python310\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Program Files\\Python310\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Program Files\\Python310\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Program Files\\Python310\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Program Files\\Python310\\lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Program Files\\Python310\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Program Files\\Python310\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Program Files\\Python310\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Program Files\\Python310\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Program Files\\Python310\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Program Files\\Python310\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Program Files\\Python310\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Program Files\\Python310\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Program Files\\Python310\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Program Files\\Python310\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Program Files\\Python310\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Program Files\\Python310\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Program Files\\Python310\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Program Files\\Python310\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Program Files\\Python310\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Program Files\\Python310\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('attr',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\attr\\__init__.py',
   'PYMODULE'),
  ('attr._cmp',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\attr\\_cmp.py',
   'PYMODULE'),
  ('attr._compat',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._config',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('attr._funcs',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._make',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\attr\\_make.py',
   'PYMODULE'),
  ('attr._next_gen',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._version_info',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr.converters',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.filters',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.setters',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr.validators',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('attrs',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\attrs\\__init__.py',
   'PYMODULE'),
  ('attrs.converters',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\attrs\\converters.py',
   'PYMODULE'),
  ('attrs.exceptions',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\attrs\\exceptions.py',
   'PYMODULE'),
  ('attrs.filters',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\attrs\\filters.py',
   'PYMODULE'),
  ('attrs.setters',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\attrs\\setters.py',
   'PYMODULE'),
  ('attrs.validators',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\attrs\\validators.py',
   'PYMODULE'),
  ('base64', 'C:\\Program Files\\Python310\\lib\\base64.py', 'PYMODULE'),
  ('bdb', 'C:\\Program Files\\Python310\\lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'C:\\Program Files\\Python310\\lib\\bisect.py', 'PYMODULE'),
  ('blinker',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\blinker\\__init__.py',
   'PYMODULE'),
  ('blinker._utilities',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\blinker\\_utilities.py',
   'PYMODULE'),
  ('blinker.base',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\blinker\\base.py',
   'PYMODULE'),
  ('bz2', 'C:\\Program Files\\Python310\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'C:\\Program Files\\Python310\\lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('click',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click._compat',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.core',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click.globals',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.parser',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click.testing',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('click.types',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('cmd', 'C:\\Program Files\\Python310\\lib\\cmd.py', 'PYMODULE'),
  ('code', 'C:\\Program Files\\Python310\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\Program Files\\Python310\\lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Program Files\\Python310\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Program Files\\Python310\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Program Files\\Python310\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Program Files\\Python310\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Program Files\\Python310\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Program Files\\Python310\\lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Program Files\\Python310\\lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'C:\\Program Files\\Python310\\lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.padding',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv', 'C:\\Program Files\\Python310\\lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'C:\\Program Files\\Python310\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Program Files\\Python310\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Program Files\\Python310\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Program Files\\Python310\\lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'C:\\Program Files\\Python310\\lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'C:\\Program Files\\Python310\\lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'C:\\Program Files\\Python310\\lib\\difflib.py', 'PYMODULE'),
  ('dis', 'C:\\Program Files\\Python310\\lib\\dis.py', 'PYMODULE'),
  ('doctest', 'C:\\Program Files\\Python310\\lib\\doctest.py', 'PYMODULE'),
  ('email',
   'C:\\Program Files\\Python310\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Program Files\\Python310\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Program Files\\Python310\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Program Files\\Python310\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Program Files\\Python310\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Program Files\\Python310\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Program Files\\Python310\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Program Files\\Python310\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Program Files\\Python310\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Program Files\\Python310\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Program Files\\Python310\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Program Files\\Python310\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Program Files\\Python310\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Program Files\\Python310\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Program Files\\Python310\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Program Files\\Python310\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Program Files\\Python310\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Program Files\\Python310\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Program Files\\Python310\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Program Files\\Python310\\lib\\email\\utils.py',
   'PYMODULE'),
  ('flask',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask\\__init__.py',
   'PYMODULE'),
  ('flask.app',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask\\app.py',
   'PYMODULE'),
  ('flask.blueprints',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('flask.cli',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask\\cli.py',
   'PYMODULE'),
  ('flask.config',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.ctx',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask\\ctx.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.globals',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.helpers',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.json',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE'),
  ('flask.json.provider',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask\\json\\provider.py',
   'PYMODULE'),
  ('flask.json.tag',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('flask.logging',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('flask.sansio', '-', 'PYMODULE'),
  ('flask.sansio.app',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask\\sansio\\app.py',
   'PYMODULE'),
  ('flask.sansio.blueprints',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask\\sansio\\blueprints.py',
   'PYMODULE'),
  ('flask.sansio.scaffold',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask\\sansio\\scaffold.py',
   'PYMODULE'),
  ('flask.sessions',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.signals',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('flask.templating',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('flask.testing',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('flask.typing',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('flask.views',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask\\views.py',
   'PYMODULE'),
  ('flask.wrappers',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('flask_cors',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask_cors\\__init__.py',
   'PYMODULE'),
  ('flask_cors.core',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask_cors\\core.py',
   'PYMODULE'),
  ('flask_cors.decorator',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask_cors\\decorator.py',
   'PYMODULE'),
  ('flask_cors.extension',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask_cors\\extension.py',
   'PYMODULE'),
  ('flask_cors.version',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask_cors\\version.py',
   'PYMODULE'),
  ('flask_restx',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask_restx\\__init__.py',
   'PYMODULE'),
  ('flask_restx.__about__',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask_restx\\__about__.py',
   'PYMODULE'),
  ('flask_restx._http',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask_restx\\_http.py',
   'PYMODULE'),
  ('flask_restx.api',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask_restx\\api.py',
   'PYMODULE'),
  ('flask_restx.apidoc',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask_restx\\apidoc.py',
   'PYMODULE'),
  ('flask_restx.cors',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask_restx\\cors.py',
   'PYMODULE'),
  ('flask_restx.errors',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask_restx\\errors.py',
   'PYMODULE'),
  ('flask_restx.fields',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask_restx\\fields.py',
   'PYMODULE'),
  ('flask_restx.inputs',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask_restx\\inputs.py',
   'PYMODULE'),
  ('flask_restx.marshalling',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask_restx\\marshalling.py',
   'PYMODULE'),
  ('flask_restx.mask',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask_restx\\mask.py',
   'PYMODULE'),
  ('flask_restx.model',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask_restx\\model.py',
   'PYMODULE'),
  ('flask_restx.namespace',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask_restx\\namespace.py',
   'PYMODULE'),
  ('flask_restx.postman',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask_restx\\postman.py',
   'PYMODULE'),
  ('flask_restx.representations',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask_restx\\representations.py',
   'PYMODULE'),
  ('flask_restx.reqparse',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask_restx\\reqparse.py',
   'PYMODULE'),
  ('flask_restx.resource',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask_restx\\resource.py',
   'PYMODULE'),
  ('flask_restx.swagger',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask_restx\\swagger.py',
   'PYMODULE'),
  ('flask_restx.utils',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\flask_restx\\utils.py',
   'PYMODULE'),
  ('fnmatch', 'C:\\Program Files\\Python310\\lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'C:\\Program Files\\Python310\\lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'C:\\Program Files\\Python310\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'C:\\Program Files\\Python310\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'C:\\Program Files\\Python310\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'C:\\Program Files\\Python310\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'C:\\Program Files\\Python310\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'C:\\Program Files\\Python310\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\Program Files\\Python310\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'C:\\Program Files\\Python310\\lib\\hmac.py', 'PYMODULE'),
  ('html', 'C:\\Program Files\\Python310\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'C:\\Program Files\\Python310\\lib\\html\\entities.py',
   'PYMODULE'),
  ('http', 'C:\\Program Files\\Python310\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client',
   'C:\\Program Files\\Python310\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Program Files\\Python310\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Program Files\\Python310\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Program Files\\Python310\\lib\\http\\server.py',
   'PYMODULE'),
  ('idna',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Program Files\\Python310\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Program Files\\Python310\\lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._adapters',
   'C:\\Program Files\\Python310\\lib\\importlib\\_adapters.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Program Files\\Python310\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Program Files\\Python310\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._common',
   'C:\\Program Files\\Python310\\lib\\importlib\\_common.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Program Files\\Python310\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Program Files\\Python310\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Program Files\\Python310\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Program Files\\Python310\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Program Files\\Python310\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Program Files\\Python310\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Program Files\\Python310\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Program Files\\Python310\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Program Files\\Python310\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Program Files\\Python310\\lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Program Files\\Python310\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Program Files\\Python310\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib_resources',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('importlib_resources._adapters',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('importlib_resources._common',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('importlib_resources._functional',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\importlib_resources\\_functional.py',
   'PYMODULE'),
  ('importlib_resources._itertools',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('importlib_resources.abc',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('importlib_resources.compat',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\importlib_resources\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_resources.compat.py39',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\importlib_resources\\compat\\py39.py',
   'PYMODULE'),
  ('importlib_resources.future',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\importlib_resources\\future\\__init__.py',
   'PYMODULE'),
  ('importlib_resources.future.adapters',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\importlib_resources\\future\\adapters.py',
   'PYMODULE'),
  ('importlib_resources.readers',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('inspect', 'C:\\Program Files\\Python310\\lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Program Files\\Python310\\lib\\ipaddress.py', 'PYMODULE'),
  ('itsdangerous',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('jinja2',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json', 'C:\\Program Files\\Python310\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'C:\\Program Files\\Python310\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Program Files\\Python310\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Program Files\\Python310\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('jsonschema',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jsonschema\\__init__.py',
   'PYMODULE'),
  ('jsonschema._format',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jsonschema\\_format.py',
   'PYMODULE'),
  ('jsonschema._keywords',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jsonschema\\_keywords.py',
   'PYMODULE'),
  ('jsonschema._legacy_keywords',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jsonschema\\_legacy_keywords.py',
   'PYMODULE'),
  ('jsonschema._types',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jsonschema\\_types.py',
   'PYMODULE'),
  ('jsonschema._typing',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jsonschema\\_typing.py',
   'PYMODULE'),
  ('jsonschema._utils',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jsonschema\\_utils.py',
   'PYMODULE'),
  ('jsonschema.exceptions',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jsonschema\\exceptions.py',
   'PYMODULE'),
  ('jsonschema.protocols',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jsonschema\\protocols.py',
   'PYMODULE'),
  ('jsonschema.validators',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jsonschema\\validators.py',
   'PYMODULE'),
  ('jsonschema_specifications',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jsonschema_specifications\\__init__.py',
   'PYMODULE'),
  ('jsonschema_specifications._core',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\jsonschema_specifications\\_core.py',
   'PYMODULE'),
  ('logging',
   'C:\\Program Files\\Python310\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'C:\\Program Files\\Python310\\lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mimetypes', 'C:\\Program Files\\Python310\\lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Program Files\\Python310\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'C:\\Program Files\\Python310\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path',
   'C:\\Program Files\\Python310\\lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers', 'C:\\Program Files\\Python310\\lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'C:\\Program Files\\Python310\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'C:\\Program Files\\Python310\\lib\\optparse.py', 'PYMODULE'),
  ('pathlib', 'C:\\Program Files\\Python310\\lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'C:\\Program Files\\Python310\\lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'C:\\Program Files\\Python310\\lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'C:\\Program Files\\Python310\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'C:\\Program Files\\Python310\\lib\\platform.py', 'PYMODULE'),
  ('pprint', 'C:\\Program Files\\Python310\\lib\\pprint.py', 'PYMODULE'),
  ('py_compile',
   'C:\\Program Files\\Python310\\lib\\py_compile.py',
   'PYMODULE'),
  ('pydoc', 'C:\\Program Files\\Python310\\lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'C:\\Program Files\\Python310\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Program Files\\Python310\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pytz',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue', 'C:\\Program Files\\Python310\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'C:\\Program Files\\Python310\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Program Files\\Python310\\lib\\random.py', 'PYMODULE'),
  ('referencing',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\referencing\\__init__.py',
   'PYMODULE'),
  ('referencing._attrs',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\referencing\\_attrs.py',
   'PYMODULE'),
  ('referencing._core',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\referencing\\_core.py',
   'PYMODULE'),
  ('referencing.exceptions',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\referencing\\exceptions.py',
   'PYMODULE'),
  ('referencing.jsonschema',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\referencing\\jsonschema.py',
   'PYMODULE'),
  ('referencing.typing',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\referencing\\typing.py',
   'PYMODULE'),
  ('requests',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\Program Files\\Python310\\lib\\rlcompleter.py',
   'PYMODULE'),
  ('rpds',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\rpds\\__init__.py',
   'PYMODULE'),
  ('runpy', 'C:\\Program Files\\Python310\\lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'C:\\Program Files\\Python310\\lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'C:\\Program Files\\Python310\\lib\\selectors.py', 'PYMODULE'),
  ('shlex', 'C:\\Program Files\\Python310\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'C:\\Program Files\\Python310\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Program Files\\Python310\\lib\\signal.py', 'PYMODULE'),
  ('socket', 'C:\\Program Files\\Python310\\lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'C:\\Program Files\\Python310\\lib\\socketserver.py',
   'PYMODULE'),
  ('ssl', 'C:\\Program Files\\Python310\\lib\\ssl.py', 'PYMODULE'),
  ('statistics',
   'C:\\Program Files\\Python310\\lib\\statistics.py',
   'PYMODULE'),
  ('string', 'C:\\Program Files\\Python310\\lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'C:\\Program Files\\Python310\\lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Program Files\\Python310\\lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig', 'C:\\Program Files\\Python310\\lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'C:\\Program Files\\Python310\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\Program Files\\Python310\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\Program Files\\Python310\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Program Files\\Python310\\lib\\threading.py', 'PYMODULE'),
  ('token', 'C:\\Program Files\\Python310\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Program Files\\Python310\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc',
   'C:\\Program Files\\Python310\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty', 'C:\\Program Files\\Python310\\lib\\tty.py', 'PYMODULE'),
  ('typing', 'C:\\Program Files\\Python310\\lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Program Files\\Python310\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Program Files\\Python310\\lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Program Files\\Python310\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Program Files\\Python310\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Program Files\\Python310\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Program Files\\Python310\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Program Files\\Python310\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Program Files\\Python310\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Program Files\\Python310\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Program Files\\Python310\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Program Files\\Python310\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Program Files\\Python310\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Program Files\\Python310\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Program Files\\Python310\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Program Files\\Python310\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Program Files\\Python310\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uu', 'C:\\Program Files\\Python310\\lib\\uu.py', 'PYMODULE'),
  ('uuid', 'C:\\Program Files\\Python310\\lib\\uuid.py', 'PYMODULE'),
  ('webbrowser',
   'C:\\Program Files\\Python310\\lib\\webbrowser.py',
   'PYMODULE'),
  ('werkzeug',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\datastructures\\__init__.py',
   'PYMODULE'),
  ('werkzeug.datastructures.accept',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\datastructures\\accept.py',
   'PYMODULE'),
  ('werkzeug.datastructures.auth',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\datastructures\\auth.py',
   'PYMODULE'),
  ('werkzeug.datastructures.cache_control',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\datastructures\\cache_control.py',
   'PYMODULE'),
  ('werkzeug.datastructures.csp',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\datastructures\\csp.py',
   'PYMODULE'),
  ('werkzeug.datastructures.etag',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\datastructures\\etag.py',
   'PYMODULE'),
  ('werkzeug.datastructures.file_storage',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\datastructures\\file_storage.py',
   'PYMODULE'),
  ('werkzeug.datastructures.headers',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\datastructures\\headers.py',
   'PYMODULE'),
  ('werkzeug.datastructures.mixins',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\datastructures\\mixins.py',
   'PYMODULE'),
  ('werkzeug.datastructures.range',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\datastructures\\range.py',
   'PYMODULE'),
  ('werkzeug.datastructures.structures',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\datastructures\\structures.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.http',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.local',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.security',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug.test',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'C:\\Users\\<USER>\\PycharmProjects\\139mail_py\\.venv\\lib\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('xml', 'C:\\Program Files\\Python310\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.parsers',
   'C:\\Program Files\\Python310\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Program Files\\Python310\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Program Files\\Python310\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Program Files\\Python310\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Program Files\\Python310\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Program Files\\Python310\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Program Files\\Python310\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Program Files\\Python310\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Program Files\\Python310\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Program Files\\Python310\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile', 'C:\\Program Files\\Python310\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'C:\\Program Files\\Python310\\lib\\zipimport.py', 'PYMODULE')])
