import requests

cookies = {
    'cbauto': 'always',
    'WT_FPC': 'id=287626f7ee25d003c6c1753790467971:lv=1753790467971:ss=1753790467971',
    '_139_index_login': '17537904680252000223383331',
    '_139_index_isLoginType': '0',
    'cookieLen': '9',
    'PICTUREUIN': 'wVeNYxd9D7JyNlmrtW/IPg==',
    'PICTURELOGIN': 'YmY3OWU1NTA5OTE1ZTViNjI0NzFhZDhiNTMwYWJ8NDA2MzE3OTg4fDE3NTM3OTA0NzMxMDZ8UklDSElORk84ODg=',
    'agentid': 'a9a50791-93fb-4841-8caa-c93276e2640f',
}

headers = {
    'accept': 'text/javascript',
    'accept-language': 'zh-CN,zh;q=0.9',
    'content-type': 'application/xml',
    'origin': 'https://mail.10086.cn',
    'priority': 'u=1, i',
    'sec-ch-ua': '"Not)A;<PERSON>";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }

data = '<object>\n  <int name="clientId">1</int>\n  <string name="verifyCode">%257B%2522k%2522%253A%2522%25E4%25BB%25AC%25E7%25BE%25A1%25E5%25BA%2594%2522%252C%2522p%2522%253A%255B%257B%2522x%2522%253A233%252C%2522y%2522%253A0%257D%252C%257B%2522x%2522%253A164%252C%2522y%2522%253A76%257D%252C%257B%2522x%2522%253A239%252C%2522y%2522%253A79%257D%255D%252C%2522c%2522%253A%2522c386945d318d55e39b449e0c95b53ba5%2522%257D</string>\n  <string name="userName">13929010110</string>\n</object>'

response = requests.post(
    'https://mail.10086.cn/s?func=login:checkNewPictureCode&cguid=2001191520716&randnum=0.7360374850995629',
    cookies=cookies,
    headers=headers,
    data=data,
)
print(response.text)