import requests

cookies = {
    '_139_login_agreement': '1',
    '_139_index_isLoginType': '0',
    'cookieLen': '8',
    'behaviorid': '1003_9',
    'cookiepartid8881': '12',
    'ut8881': '2',
    'cookiepartid': '12',
    'Login_UserNumber': 'MTM4MDgwMTU5NzE=',
    'UserData': '{}',
    'SkinPath28881': '',
    'rmUin8881': '1841955885',
    'provCode8881': '26',
    'areaCode8881': '2603',
    'UserNowState8881': '-1',
    'UserNowState': '-1',
    '_139_login_version': '60',
    'loginProcessFlag': '',
    'taskIdCloud': '',
    'agentid': 'ffe849ad-adbc-4955-8113-6999b937b87d&%E8%AA%93%E5%A9%BF%E7%89%99',
    'WT_FPC': 'id=2d417304dcebf4abfa41753818568560:lv=1753818568560:ss=1753818568560',
    'a_l': '1769370569000|2982224254',
    'a_l2': '1769370569000|12|MTM4MDgwMTU5NzF8MjAyNi0wMS0yNiAwMzo0OToyOXx5Q1pXTEJrcWgxQVJGUUFwZkxHN3ZheWpFenhvUnpQeUhaRzczZHVhQzFqbGw4alJFQ2MybGdLVFVIY0NuVExrYTZ6bys0SU5IS21CVHRnVnRQQVVoZz09fDBjYzFmOTc2M2U1NWZmYzE2ZGU4YTYyMDk2YmZlNDA5',
    'RMKEY': 'b636ddceb5163b31',
    'Os_SSo_Sid': '03c1MzgxODU2OTAwMDg3MjQ104870593000007',
    'isReadUser03c1MzgxODU2OTAwMDg3MjQ104870593000007': '0',
}

headers = {
    'Accept': '*/*',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'Connection': 'keep-alive',
    'Content-Type': 'application/xml',
    'Origin': 'https://smsrebuild10.mail.10086.cn',
    'Referer': 'https://smsrebuild10.mail.10086.cn/proxy_2.htm',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-origin',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    # 'Cookie': '_139_login_agreement=1; _139_index_isLoginType=0; cookieLen=8; behaviorid=1003_9; cookiepartid8881=12; ut8881=2; cookiepartid=12; Login_UserNumber=MTM4MDgwMTU5NzE=; UserData={}; SkinPath28881=; rmUin8881=1841955885; provCode8881=26; areaCode8881=2603; UserNowState8881=-1; UserNowState=-1; _139_login_version=60; loginProcessFlag=; taskIdCloud=; agentid=ffe849ad-adbc-4955-8113-6999b937b87d&%E8%AA%93%E5%A9%BF%E7%89%99; WT_FPC=id=2d417304dcebf4abfa41753818568560:lv=1753818568560:ss=1753818568560; a_l=1769370569000|2982224254; a_l2=1769370569000|12|MTM4MDgwMTU5NzF8MjAyNi0wMS0yNiAwMzo0OToyOXx5Q1pXTEJrcWgxQVJGUUFwZkxHN3ZheWpFenhvUnpQeUhaRzczZHVhQzFqbGw4alJFQ2MybGdLVFVIY0NuVExrYTZ6bys0SU5IS21CVHRnVnRQQVVoZz09fDBjYzFmOTc2M2U1NWZmYzE2ZGU4YTYyMDk2YmZlNDA5; RMKEY=b636ddceb5163b31; Os_SSo_Sid=03c1MzgxODU2OTAwMDg3MjQ104870593000007; isReadUser03c1MzgxODU2OTAwMDg3MjQ104870593000007=0',
}

data = '<object>\n  <string name="startTime">2025-07-30 04:09:24</string>\n  <string name="endTime">2025-07-30 07:09:24</string>\n  <string name="meetingName">13808015971发起的会议2025-07-30 04:09</string>\n  <string name="roomId">7VfDQZRUfyFOkS3OzLf9Nxa1qh51N3jg</string>\n  <int name="openVideo">0</int>\n</object>'.encode()

response = requests.post(
    'https://smsrebuild10.mail.10086.cn/file/disk?func=disk:openVirtualMeeting&sid=03c1MzgxODU2OTAwMDg3MjQ104870593000007&comefrom=54&stime=1753819764659&cguid=0409246590244&randnum=0.30453501861724297',
    cookies=cookies,
    headers=headers,
    data=data,
)
print(response.text)