import requests

cookies = {
    '_139_login_agreement': '1',
    '_139_index_isLoginType': '0',
    'cookieLen': '8',
    'behaviorid': '1003_9',
    'cookiepartid8881': '12',
    'ut8881': '2',
    'cookiepartid': '12',
    'Login_UserNumber': 'MTM4MDgwMTU5NzE=',
    'UserData': '{}',
    'SkinPath28881': '',
    'rmUin8881': '1841955885',
    'provCode8881': '26',
    'areaCode8881': '2603',
    'UserNowState8881': '-1',
    'UserNowState': '-1',
    '_139_login_version': '60',
    'loginProcessFlag': '',
    'taskIdCloud': '',
    'agentid': 'ffe849ad-adbc-4955-8113-6999b937b87d&%E8%AA%93%E5%A9%BF%E7%89%99',
    'WT_FPC': 'id=2d417304dcebf4abfa41753818568560:lv=1753818568560:ss=1753818568560',
    'a_l': '1769370569000|2982224254',
    'a_l2': '1769370569000|12|MTM4MDgwMTU5NzF8MjAyNi0wMS0yNiAwMzo0OToyOXx5Q1pXTEJrcWgxQVJGUUFwZkxHN3ZheWpFenhvUnpQeUhaRzczZHVhQzFqbGw4alJFQ2MybGdLVFVIY0NuVExrYTZ6bys0SU5IS21CVHRnVnRQQVVoZz09fDBjYzFmOTc2M2U1NWZmYzE2ZGU4YTYyMDk2YmZlNDA5',
    'RMKEY': 'b636ddceb5163b31',
    'Os_SSo_Sid': '03c1MzgxODU2OTAwMDg3MjQ104870593000007',
    'isReadUser03c1MzgxODU2OTAwMDg3MjQ104870593000007': '0',
    'welcome': 's%3ACZMelfS6OPs3tQwZwtDdiNRUUnJDjh3J.%2B4ljOdOT%2Bsup7aWItAaCI5sssL0c0qAbYpWhJUXV1VA',
}

headers = {
    'accept': '*/*',
    'accept-language': 'zh-CN,zh;q=0.9',
    'content-type': 'application/xml',
    'origin': 'https://appmail10.mail.10086.cn',
    'priority': 'u=1, i',
    'referer': 'https://appmail10.mail.10086.cn/m6/html/index.html?sid=03c1MzgxODU2OTAwMDg3MjQ104870593000007&rnd=627&tab=&comefrom=54&v=60&k=8881&resource=indexLogin&clientid=1003&auto=on&cguid=0209163054510&mtime=193',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
    'x-tingyun': 'c=B|C1B2rcnhXuE;x=221c1edfa94449d8;u=gmjs#30818902204743AE191356503AAAA64D9E08759A0F2E0844B7FFF1E539933E83B73B4215CA022100D8C270840CC0C1662033E256874807C5D83E2ABE7A8211EA49E77B3EEE4E82200420F331B803E5676761C63C04E5C2FF3F9758E9660B60E30DD0C186F87439F1626C04206FA078C5D9D7034823FF3AB2DB3DE6F49AFF85CC11D65FEC5A76617648FBEA9C#6ED09FD0DBD1985028CAB7CA9B33355C',
    # 'cookie': '_139_login_agreement=1; _139_index_isLoginType=0; cookieLen=8; behaviorid=1003_9; cookiepartid8881=12; ut8881=2; cookiepartid=12; Login_UserNumber=MTM4MDgwMTU5NzE=; UserData={}; SkinPath28881=; rmUin8881=1841955885; provCode8881=26; areaCode8881=2603; UserNowState8881=-1; UserNowState=-1; _139_login_version=60; loginProcessFlag=; taskIdCloud=; agentid=ffe849ad-adbc-4955-8113-6999b937b87d&%E8%AA%93%E5%A9%BF%E7%89%99; WT_FPC=id=2d417304dcebf4abfa41753818568560:lv=1753818568560:ss=1753818568560; a_l=1769370569000|2982224254; a_l2=1769370569000|12|MTM4MDgwMTU5NzF8MjAyNi0wMS0yNiAwMzo0OToyOXx5Q1pXTEJrcWgxQVJGUUFwZkxHN3ZheWpFenhvUnpQeUhaRzczZHVhQzFqbGw4alJFQ2MybGdLVFVIY0NuVExrYTZ6bys0SU5IS21CVHRnVnRQQVVoZz09fDBjYzFmOTc2M2U1NWZmYzE2ZGU4YTYyMDk2YmZlNDA5; RMKEY=b636ddceb5163b31; Os_SSo_Sid=03c1MzgxODU2OTAwMDg3MjQ104870593000007; isReadUser03c1MzgxODU2OTAwMDg3MjQ104870593000007=0; welcome=s%3ACZMelfS6OPs3tQwZwtDdiNRUUnJDjh3J.%2B4ljOdOT%2Bsup7aWItAaCI5sssL0c0qAbYpWhJUXV1VA',
}

data = '<object>\n  <object name="attrs">\n    <int name="id">0.*****************</int>\n    <string name="mid" />\n    <string name="messageId" />\n    <string name="account"><EMAIL></string>\n    <string name="to"><EMAIL></string>\n    <string name="cc" />\n    <string name="bcc" />\n    <int name="showOneRcpt">0</int>\n    <int name="isHtml">1</int>\n    <string name="subject">【会议邀请】 测试会议</string>\n    <string name="content">&lt;div class=&quot;video-ppt&quot; style=&quot;margin: 0 auto;padding: 0 ;color: #444;font: 14px/1.5 \'Microsoft YaHei\',\'SimSun\';border: 1px solid #e6e6e6;box-shadow: 0 0 3px 3px #f5f5f5;box-sizing: border-box;max-width: 600px;&quot;&gt;&lt;h3 style=&quot;font-size: 18px;margin: 0;padding:18px 5px;line-height: 22px;background: #f4faff;border-bottom: 1px solid #e4e4e4;text-align: center&quot;&gt;【会议邀请】测试会议&lt;/h3&gt;&lt;div style=&quot;padding:20px;box-sizing:border-box&quot;&gt;&lt;table&gt;&lt;tr&gt;&lt;td height=\'30\' style=&quot;color: rgb(153,153,153);&quot;&gt;主题：测试会议&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td height=\'30\' style=&quot;color: rgb(153,153,153);&quot;&gt;时间：2025年07月30日 05:00  至  2025年07月30日 06:00&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td height=\'30\' style=&quot;color: rgb(153,153,153);&quot;&gt;会议地址：https://mail.10086.cn?id=meeting&amp;flag=IwNYRjk9LFmAzogkJO0oIOXl7WRc6%2BQd&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td height=\'30\' style=&quot;color: rgb(153,153,153);&quot;&gt;温馨提示：需登录139邮箱参会&lt;/td&gt;&lt;/tr&gt;&lt;/table&gt;&lt;/div&gt;&lt;div id=&quot;onlinemeeting&quot; style=&quot;font-size: 18px;position: relative;margin: 0;padding:15px 0px;line-height: 20px;background: #fefefe;text-align: center;height:20px;&quot;&gt;&lt;a id=&quot;saveInvoice&quot; target=&quot;_self&quot; title=&quot;加入会议&quot; href=&quot;https://mail.10086.cn?id=meeting&amp;flag=IwNYRjk9LFmAzogkJO0oIOXl7WRc6%2BQd&quot; class=&quot;icoG&quot; style=&quot;padding:4px 0px;position: absolute;left: 36%;text-decoration: none;display: block;width:112px;height: 20px;font-size: 14px;overflow: hidden;zoom: 1;line-height: 20px;color: #fff;text-align: center;white-space: nowrap;border: 1px solid #00a513;border-radius: 4px;background: #00bd16;background: -moz-gradient(linear, 0 0, 0 100%, from(#00c417), to(#00b615));background: -webkit-gradient(linear, 0 0, 0 100%, from(#00C417), to(#00b615));background: linear-gradient(#00c417 0%,#00b615 100%);&quot;&gt;&lt;span class=&quot;p_relative&quot; style=&quot;display:block;height: 20px;line-height: 20px;padding: 0 14px;text-align: center;padding: 0 14px;vertical-align: top;cursor: pointer;&quot;&gt;加入会议&lt;/span&gt;&lt;/a&gt;&lt;/div&gt;&lt;/div&gt;</string>\n    <int name="priority">3</int>\n    <int name="signatureId">0</int>\n    <int name="stationeryId">0</int>\n    <int name="saveSentCopy">1</int>\n    <int name="requestReadReceipt">0</int>\n    <int name="inlineResources">1</int>\n    <int name="cloudAttach">0</int>\n    <int name="largeAttach">0</int>\n    <int name="scheduleDate">0</int>\n    <int name="normalizeRfc822">0</int>\n    <array name="attachments">\n    </array>\n  </object>\n  <string name="action">deliver</string>\n  <int name="replyNotify">0</int>\n  <int name="returnInfo">1</int>\n</object>'.encode()

response = requests.post(
    'https://appmail10.mail.10086.cn/RmWeb/mail?func=mbox:compose&comefrom=5&categroyId=103000000&sid=03c1MzgxODU2OTAwMDg3MjQ104870593000007&comefrom=54&guid=09888be35cd98c&stime=1753820985844&cguid=0429458449862',
    cookies=cookies,
    headers=headers,
    data=data,
)
print(response.text)