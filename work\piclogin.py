import requests

cookies = {
    'cbauto': 'always',
    'WT_FPC': 'id=287626f7ee25d003c6c1753790467971:lv=1753790467971:ss=1753790467971',
    '_139_index_login': '17537904680252000223383331',
    '_139_index_isLoginType': '0',
    'cookieLen': '9',
    'PICTUREUIN': 'wVeNYxd9D7JyNlmrtW/IPg==',
    'PICTURELOGIN': 'YmY3OWU1NTA5OTE1ZTViNjI0NzFhZDhiNTMwYWJ8NDA2MzE3OTg4fDE3NTM3OTA0NzMxMDZ8UklDSElORk84ODg=',
    'agentid': 'a9a50791-93fb-4841-8caa-c93276e2640f',
}

headers = {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'accept-language': 'zh-CN,zh;q=0.9',
    'cache-control': 'max-age=0',
    'content-type': 'application/x-www-form-urlencoded',
    'origin': 'https://mail.10086.cn',
    'priority': 'u=0, i',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'document',
    'sec-fetch-mode': 'navigate',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-user': '?1',
    'upgrade-insecure-requests': '1',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }

data = {
    'VerifyCode': '%257B%2522k%2522%253A%2522%25E4%25BB%25AC%25E7%25BE%25A1%25E5%25BA%2594%2522%252C%2522p%2522%253A%255B%257B%2522x%2522%253A233%252C%2522y%2522%253A0%257D%252C%257B%2522x%2522%253A164%252C%2522y%2522%253A76%257D%252C%257B%2522x%2522%253A239%252C%2522y%2522%253A79%257D%255D%252C%2522c%2522%253A%2522c386945d318d55e39b449e0c95b53ba5%2522%257D',
    'u': '13929010110',
    's': '1',
}

response = requests.post('https://mail.10086.cn/s?func=login:picLogin', cookies=cookies, headers=headers, data=data,allow_redirects=False)
print(response.status_code)
print(response.headers)
