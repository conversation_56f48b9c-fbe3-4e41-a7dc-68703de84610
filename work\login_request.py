#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
带AES加解密的登录请求脚本 - 支持并发测试
"""

import requests
import json
import base64
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad


class AESCryptoTool:
    """AES加密解密工具类 - 从aes_tool.py提取的核心功能"""
    
    def __init__(self):
        # 使用与服务器端相同的32字节密钥
        self.key = b'139mail_api_2025_secret_key_3200'
        print(f"🔑 使用密钥: {self.key.decode('utf-8')}")
    
    def encrypt(self, plaintext):
        """加密明文 - 使用ECB模式"""
        try:
            # 如果输入是字符串，转换为字节
            if isinstance(plaintext, str):
                plaintext = plaintext.encode('utf-8')
            
            # 使用ECB模式，不需要IV
            cipher = AES.new(self.key, AES.MODE_ECB)
            
            # 填充并加密
            padded_data = pad(plaintext, AES.block_size)
            encrypted_data = cipher.encrypt(padded_data)
            
            # 直接Base64编码加密数据
            encrypted_base64 = base64.b64encode(encrypted_data).decode('utf-8')
            
            return encrypted_base64
            
        except Exception as e:
            return f"❌ 加密失败: {str(e)}"
    
    def decrypt(self, encrypted_data):
        """解密密文 - 使用ECB模式"""
        try:
            # Base64解码
            encrypted_bytes = base64.b64decode(encrypted_data)
            
            # 使用ECB模式解密，不需要IV
            cipher = AES.new(self.key, AES.MODE_ECB)
            decrypted_padded = cipher.decrypt(encrypted_bytes)
            
            # 去除填充
            decrypted_data = unpad(decrypted_padded, AES.block_size)
            
            # 尝试解析为JSON，如果失败则返回原始字符串
            try:
                return json.loads(decrypted_data.decode('utf-8'))
            except json.JSONDecodeError:
                return decrypted_data.decode('utf-8')
                
        except Exception as e:
            return f"❌ 解密失败: {str(e)}"


# 测试账号列表
TEST_ACCOUNTS = [
    {"username": "***********", "password": "*********"},
    {"username": "***********", "password": "jimmy1979"},
    {"username": "***********", "password": "2058354"},
    {"username": "***********", "password": "cc400129"},
    {"username": "***********", "password": "ever0623"}
]

# 线程锁，用于同步输出
print_lock = threading.Lock()

def thread_safe_print(*args, **kwargs):
    """线程安全的打印函数"""
    with print_lock:
        print(*args, **kwargs)


def get_proxy_from_api():
    """从API接口获取代理地址"""
    api_url = "http://bapi.51daili.com/traffic/getip?linePoolIndex=1&packid=12&time=11&qty=1&port=1&format=txt&ct=0&usertype=17&uid=44207&accessName=x201004&accessPassword=7dd204acfad205c0d9a4cd0b32c746a8&skey=autoaddwhiteip"
    
    try:
        response = requests.get(api_url, timeout=10)
        
        if response.status_code == 200:
            proxy_address = response.text.strip()
            return f"http://{proxy_address}"
        else:
            return None
            
    except Exception as e:
        return None


def send_login_request_single(crypto, login_data, account_index, max_retries=2):
    """发送单个登录请求"""
    username = login_data['username']
    thread_id = threading.current_thread().ident
    
    thread_safe_print(f"🚀 [账号{account_index+1}] 线程{thread_id} 开始测试账号: {username}")
    
    start_time = time.time()
    
    for attempt in range(max_retries):
        try:
            # 将登录数据转换为JSON字符串并加密
            login_json = json.dumps(login_data, ensure_ascii=False)
            encrypted_data = crypto.encrypt(login_json)
            
            if encrypted_data.startswith("❌"):
                thread_safe_print(f"❌ [账号{account_index+1}] 加密失败: {encrypted_data}")
                return {
                    'account_index': account_index,
                    'username': username,
                    'success': False,
                    'message': '加密失败',
                    'duration': time.time() - start_time,
                    'thread_id': thread_id
                }
            
            # 构造加密请求
            encrypted_request = {"data": encrypted_data}
            
            # 发送加密请求
            response = requests.post(
                "http://127.0.0.1:5000/auth/login",
                json=encrypted_request,
                headers={'Content-Type': 'application/json'},
                timeout=15
            )
            
            # 获取响应数据
            response_data = response.json()
            
            # 检查响应中是否有加密数据需要解密
            if 'data' in response_data and isinstance(response_data['data'], str):
                decrypted_response = crypto.decrypt(response_data['data'])
                
                if isinstance(decrypted_response, str) and decrypted_response.startswith("❌"):
                    thread_safe_print(f"❌ [账号{account_index+1}] 解密失败: {decrypted_response}")
                    return {
                        'account_index': account_index,
                        'username': username,
                        'success': False,
                        'message': '解密失败',
                        'duration': time.time() - start_time,
                        'thread_id': thread_id
                    }
                
                if isinstance(decrypted_response, dict):
                    # 检查登录结果 - 修正判断逻辑
                    msg = decrypted_response.get('msg', '')
                    cookie = decrypted_response.get('cookie', '')
                    
                    # 如果消息是"登录成功"且有cookie，则认为登录成功
                    if msg == "登录成功" and cookie:
                        duration = time.time() - start_time
                        thread_safe_print(f"✅ [账号{account_index+1}] 登录成功! 用时: {duration:.2f}秒")
                        thread_safe_print(f"   Cookie长度: {len(cookie)} 字符")
                        return {
                            'account_index': account_index,
                            'username': username,
                            'success': True,
                            'message': msg,
                            'duration': duration,
                            'thread_id': thread_id,
                            'cookies': cookie
                        }
                    else:
                        # 登录失败，使用msg作为错误信息
                        error_msg = msg if msg else '登录失败'
                        
                        # 检查是否是代理连接失败，如果是则重试
                        if '代理连接失败' in str(error_msg) and attempt < max_retries - 1:
                            thread_safe_print(f"⚠️ [账号{account_index+1}] 代理连接失败，切换到无代理模式重试...")
                            # 移除代理设置
                            if 'proxy_type' in login_data:
                                del login_data['proxy_type']
                            if 'proxy_info' in login_data:
                                del login_data['proxy_info']
                            continue
                        
                        thread_safe_print(f"❌ [账号{account_index+1}] 登录失败: {error_msg}")
                        return {
                            'account_index': account_index,
                            'username': username,
                            'success': False,
                            'message': error_msg,
                            'duration': time.time() - start_time,
                            'thread_id': thread_id
                        }
                else:
                    thread_safe_print(f"❌ [账号{account_index+1}] 响应格式异常")
                    return {
                        'account_index': account_index,
                        'username': username,
                        'success': False,
                        'message': '响应格式异常',
                        'duration': time.time() - start_time,
                        'thread_id': thread_id
                    }
            else:
                thread_safe_print(f"❌ [账号{account_index+1}] 响应数据格式不符合预期")
                return {
                    'account_index': account_index,
                    'username': username,
                    'success': False,
                    'message': '响应数据格式不符合预期',
                    'duration': time.time() - start_time,
                    'thread_id': thread_id
                }
                
        except requests.exceptions.RequestException as e:
            error_msg = str(e)
            thread_safe_print(f"❌ [账号{account_index+1}] 请求失败: {error_msg}")
            
            # 如果是最后一次尝试，返回失败结果
            if attempt == max_retries - 1:
                return {
                    'account_index': account_index,
                    'username': username,
                    'success': False,
                    'message': f'请求失败: {error_msg}',
                    'duration': time.time() - start_time,
                    'thread_id': thread_id
                }
            else:
                thread_safe_print(f"🔄 [账号{account_index+1}] 准备重试...")
                time.sleep(1)  # 等待1秒后重试
                
        except Exception as e:
            thread_safe_print(f"❌ [账号{account_index+1}] 发生未知错误: {str(e)}")
            return {
                'account_index': account_index,
                'username': username,
                'success': False,
                'message': f'未知错误: {str(e)}',
                'duration': time.time() - start_time,
                'thread_id': thread_id
            }
    
    # 如果所有重试都失败了
    return {
        'account_index': account_index,
        'username': username,
        'success': False,
        'message': '所有重试都失败了',
        'duration': time.time() - start_time,
        'thread_id': thread_id
    }


def concurrent_login_test(max_workers=3, use_proxy=False):
    """并发登录测试"""
    print("🚀 开始并发登录测试")
    print("=" * 60)
    print(f"📊 测试账号数量: {len(TEST_ACCOUNTS)}")
    print(f"🔧 最大并发数: {max_workers}")
    print(f"🌐 使用代理: {'是' if use_proxy else '否'}")
    print("=" * 60)
    
    # 初始化加密工具
    crypto = AESCryptoTool()
    
    # 准备测试数据
    test_data = []
    for i, account in enumerate(TEST_ACCOUNTS):
        login_data = account.copy()
        
        # 如果使用代理，为每个账号获取一个代理
        if use_proxy:
            proxy_info = get_proxy_from_api()
            if proxy_info:
                login_data['proxy_type'] = 'http'
                login_data['proxy_info'] = proxy_info
                print(f"🔗 [账号{i+1}] 获取代理: {proxy_info}")
            else:
                print(f"⚠️ [账号{i+1}] 代理获取失败，使用无代理模式")
        
        test_data.append((crypto, login_data, i))
    
    print("\n🏁 开始并发测试...")
    start_time = time.time()
    
    # 使用线程池执行并发测试
    results = []
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_account = {
            executor.submit(send_login_request_single, *data): data[2] 
            for data in test_data
        }
        
        # 收集结果
        for future in as_completed(future_to_account):
            account_index = future_to_account[future]
            try:
                result = future.result()
                results.append(result)
            except Exception as exc:
                thread_safe_print(f'❌ [账号{account_index+1}] 生成异常: {exc}')
                results.append({
                    'account_index': account_index,
                    'username': TEST_ACCOUNTS[account_index]['username'],
                    'success': False,
                    'message': f'执行异常: {exc}',
                    'duration': 0,
                    'thread_id': 'unknown'
                })
    
    total_time = time.time() - start_time
    
    # 统计结果
    print("\n" + "=" * 60)
    print("📊 测试结果统计")
    print("=" * 60)
    
    success_count = 0
    failed_count = 0
    
    # 按账号顺序排序结果
    results.sort(key=lambda x: x['account_index'])
    
    for result in results:
        status = "✅ 成功" if result['success'] else "❌ 失败"
        print(f"[账号{result['account_index']+1}] {result['username']} - {status}")
        print(f"    消息: {result['message']}")
        print(f"    用时: {result['duration']:.2f}秒")
        print(f"    线程: {result['thread_id']}")
        if result['success'] and 'cookies' in result:
            print(f"    Cookie: {result['cookies'][:50]}...")
        print()
        
        if result['success']:
            success_count += 1
        else:
            failed_count += 1
    
    print("=" * 60)
    print(f"📈 总体统计:")
    print(f"   ✅ 成功: {success_count}/{len(TEST_ACCOUNTS)} ({success_count/len(TEST_ACCOUNTS)*100:.1f}%)")
    print(f"   ❌ 失败: {failed_count}/{len(TEST_ACCOUNTS)} ({failed_count/len(TEST_ACCOUNTS)*100:.1f}%)")
    print(f"   ⏱️ 总用时: {total_time:.2f}秒")
    print(f"   ⚡ 平均用时: {total_time/len(TEST_ACCOUNTS):.2f}秒/账号")
    print("=" * 60)
    
    return results


def single_account_test():
    """单账号测试"""
    print("🔍 单账号测试模式")
    print("=" * 40)
    
    # 初始化加密工具
    crypto = AESCryptoTool()
    
    # 使用第一个测试账号
    account = TEST_ACCOUNTS[0]
    print(f"📱 测试账号: {account['username']}")
    
    # 获取代理
    proxy_info = get_proxy_from_api()
    login_data = account.copy()
    
    if proxy_info:
        login_data['proxy_type'] = 'http'
        login_data['proxy_info'] = proxy_info
        print(f"🔗 使用代理: {proxy_info}")
    else:
        print("⚠️ 代理获取失败，使用无代理模式")
    
    print("\n📋 登录数据:")
    print(json.dumps(login_data, ensure_ascii=False, indent=2))
    
    # 发送登录请求
    result = send_login_request_single(crypto, login_data, 0, max_retries=3)
    
    print("\n📊 测试结果:")
    if result['success']:
        print(f"✅ 登录成功!")
        print(f"   消息: {result['message']}")
        print(f"   用时: {result['duration']:.2f}秒")
        if 'cookies' in result:
            print(f"   Cookie: {result['cookies'][:100]}...")
    else:
        print(f"❌ 登录失败!")
        print(f"   原因: {result['message']}")
        print(f"   用时: {result['duration']:.2f}秒")


def main():
    """主函数"""
    print("🔐 139邮箱登录并发测试工具")
    print("=" * 50)
    
    while True:
        print("\n请选择测试模式:")
        print("1. 单账号测试")
        print("2. 并发测试 (3线程, 无代理)")
        print("3. 并发测试 (3线程, 使用代理)")
        print("4. 并发测试 (5线程, 无代理)")
        print("5. 并发测试 (5线程, 使用代理)")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == '0':
            print("👋 再见!")
            break
        elif choice == '1':
            single_account_test()
        elif choice == '2':
            concurrent_login_test(max_workers=3, use_proxy=False)
        elif choice == '3':
            concurrent_login_test(max_workers=3, use_proxy=True)
        elif choice == '4':
            concurrent_login_test(max_workers=5, use_proxy=False)
        elif choice == '5':
            concurrent_login_test(max_workers=5, use_proxy=True)
        else:
            print("❌ 无效选择，请重新输入")
        
        input("\n按回车键继续...")


if __name__ == '__main__':
    main()
