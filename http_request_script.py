#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
139邮箱登录脚本
功能：模拟139邮箱登录流程，包含密码加密和两步请求
"""

import requests
import urllib3
import hashlib
from urllib.parse import urlparse, parse_qs, unquote, quote
import time
import random
import os
import uuid
import base64
import json

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def padding(n, m=2):
    """
    数字前置补零函数（复现JavaScript的padding逻辑）

    Args:
        n: 要补零的数字
        m: 目标长度（默认2位）

    Returns:
        str: 补零后的字符串
    """
    import math

    # JavaScript: 1 + Math.floor(Math.log(n | 1) / Math.LN10 + 10e-16)
    if n == 0:
        actual_digits = 1
    else:
        # 使用JavaScript相同的计算方式
        actual_digits = int(1 + math.floor(math.log10(abs(n | 1)) + 1e-15))

    zeros_needed = m - actual_digits
    if zeros_needed > 0:
        return "0" * zeros_needed + str(n)
    else:
        return str(n)

def generate_cguid():
    """
    生成CGUID（复现JavaScript的getCGUID算法）
    格式: HHMMSSmmm9999

    Returns:
        str: 13位CGUID字符串
    """
    from datetime import datetime

    now = datetime.now()

    # 获取时间组件
    hours = padding(now.hour, 2)           # HH: 00-23
    minutes = padding(now.minute, 2)       # MM: 00-59
    seconds = padding(now.second, 2)       # SS: 00-59
    milliseconds = padding(now.microsecond // 1000, 3)  # mmm: 000-999

    # JavaScript: Math.ceil(Math.random() * 9999)
    # 生成1-9999的随机数，补齐4位
    random_part = padding(random.randint(1, 9999), 4)

    cguid = hours + minutes + seconds + milliseconds + random_part
    return cguid

def generate_randnum():
    """
    生成randnum（复现JavaScript的Math.random()）

    Returns:
        str: 0-1之间的随机浮点数字符串
    """
    return str(random.random())

def encrypt_139_password(password):
    """
    139邮箱密码加密算法（SHA-1）
    对应JavaScript中的calcDigest函数
    
    Args:
        password (str): 用户输入的原始密码
        
    Returns:
        str: 加密后的40位十六进制SHA-1哈希值
    """
    # 添加139邮箱特有的盐值前缀
    salted_password = "fetion.com.cn:" + password
    
    # 使用SHA-1进行加密
    sha1_hash = hashlib.sha1(salted_password.encode('utf-8'))
    
    # 返回40位十六进制字符串
    return sha1_hash.hexdigest()

def step1_get_main_page(session):
    """
    第一步：访问139邮箱主页
    
    Args:
        session: requests.Session对象
        
    Returns:
        tuple: (响应对象, 是否成功)
    """
    url = "https://mail.10086.cn"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'upgrade-insecure-requests': '1',
        'sec-fetch-site': 'none',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-user': '?1',
        'sec-fetch-dest': 'document',
        'accept-language': 'zh-CN,zh;q=0.9',
        'priority': 'u=0, i'
    }
    
    # 设置初始Cookie
    session.cookies.set('WT_FPC', 'id=2554c404cae03bb44bc1753806559220:lv=1753806559220:ss=1753806559220')
    
    try:
        print("=== 第一步：访问139邮箱主页 ===")
        print(f"请求URL: {url}")
        
        response = session.get(
            url,
            headers=headers,
            verify=False,  # 禁用SSL验证
            allow_redirects=True,
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"最终URL: {response.url}")
        
        # 打印重定向历史
        if response.history:
            print("重定向历史:")
            for i, resp in enumerate(response.history, 1):
                print(f"  {i}. {resp.status_code} -> {resp.url}")
        
        # 打印获取的cookies
        print("获取的Cookies:")
        for cookie in session.cookies:
            print(f"  {cookie.name} = {cookie.value}")
        
        return response, response.status_code == 200
        
    except Exception as e:
        print(f"第一步请求失败: {e}")
        return None, False

def step2_login_request(session, username, password):
    """
    第二步：执行登录请求
    
    Args:
        session: requests.Session对象
        username: 用户名
        password: 原始密码
        
    Returns:
        tuple: (响应对象, 是否成功)
    """
    # 加密密码
    encrypted_password = encrypt_139_password(password)
    print(f"原始密码: {password}")
    print(f"加密后密码: {encrypted_password}")
    
    # 生成CGUID和时间戳
    current_time = int(time.time() * 1000)
    cguid = generate_cguid()

    url = f"https://mail.10086.cn/Login/Login.ashx?cguid={cguid}&mtime=7&_fv=4&_=41791e25e5c99c7f835c947f5483dec8601dcfd8&resource=indexLogin"

    print(f"生成的CGUID: {cguid}")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Content-Type': 'application/x-www-form-urlencoded',
        'accept-language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
        'origin': 'https://mail.10086.cn',
        'referer': f'https://mail.10086.cn/default.html?cguid={cguid}&mtime=7',
        'upgrade-insecure-requests': '1',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'same-origin',
        'sec-fetch-user': '?1',
        'priority': 'u=0, i',
        'te': 'trailers'
    }
    
    # 设置必要的cookies
    required_cookies = {
        '_139_login_agreement': '1',
        'Login_UserNumber': 'MTM3MTUyOTY4ODE=',
        'cbauto': 'always',
        'WT_FPC': f'id=2c7123e7cc30467d2eb{current_time}:lv={current_time}:ss={current_time}',
        'agentid': f'{cguid}&%E5%9F%B9%E9%97%B7%E7%A7%BB',
        'JSESSIONID': '93BE762F781E99172E0C9E67A3B03B78',
        '_139_index_clickLogin': '1',
        '_139_index_login': f'{current_time}{cguid}',
        '_139_index_isLoginType': '0',
        'cookieLen': '7'
    }
    
    for name, value in required_cookies.items():
        session.cookies.set(name, value)
    
    # 构建POST数据
    data = {
        'UserName': username,
        'passOld': '',
        'auto': 'on',
        'Password': encrypted_password,
        'webIndexPagePwdLogin': '1',
        'pwdType': '1',
        'clientId': '1003',
        'authType': '2'
    }
    
    try:
        print("\n=== 第二步：执行登录请求 ===")
        print(f"请求URL: {url}")
        print(f"用户名: {username}")
        
        response = session.post(
            url,
            headers=headers,
            data=data,
            verify=False,  # 禁用SSL验证
            allow_redirects=True,
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"最终URL: {response.url}")
        
        # 打印重定向历史
        if response.history:
            print("重定向历史:")
            for i, resp in enumerate(response.history, 1):
                print(f"  {i}. {resp.status_code} -> {resp.url}")
        
        # 打印所有cookies
        print("当前所有Cookies:")
        for cookie in session.cookies:
            print(f"  {cookie.name} = {cookie.value}")
        
        return response, response.status_code in [200, 302]

    except Exception as e:
        print(f"第二步请求失败: {e}")
        return None, False

def step3_get_captcha(session):
    """
    第三步：请求验证码图片

    Args:
        session: requests.Session对象

    Returns:
        tuple: (图片文件路径, 验证码文字, 是否成功)
    """
    # 生成随机参数
    random_param = random.random()
    url = f"https://imagecode1.mail.10086.cn/getimage?clientid=1&r={random_param}"

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'pragma': 'no-cache',
        'cache-control': 'no-cache',
        'sec-ch-ua-platform': '"Windows"',
        'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-fetch-site': 'same-site',
        'sec-fetch-mode': 'no-cors',
        'sec-fetch-dest': 'image',
        'referer': 'https://mail.10086.cn/',
        'accept-language': 'zh-CN,zh;q=0.9',
        'priority': 'i'
    }

    try:
        print("\n=== 第三步：请求验证码图片 ===")
        print(f"请求URL: {url}")

        response = session.get(
            url,
            headers=headers,
            verify=False,  # 禁用SSL验证
            allow_redirects=True,
            timeout=30
        )

        print(f"响应状态码: {response.status_code}")
        print(f"Content-Type: {response.headers.get('content-type', 'N/A')}")
        print(f"Content-Length: {response.headers.get('content-length', 'N/A')}")

        if response.status_code != 200:
            print("验证码请求失败")
            return None, None, False

        # 解析响应头中的agentid
        captcha_text = None
        agentid_cookies = []

        # 从Set-Cookie头中提取agentid
        set_cookies = response.headers.get_list('Set-Cookie') if hasattr(response.headers, 'get_list') else []
        if not set_cookies:
            # 如果get_list不可用，尝试其他方法
            set_cookie_header = response.headers.get('Set-Cookie', '')
            if set_cookie_header:
                set_cookies = [set_cookie_header]

        print("响应头中的Set-Cookie:")
        for cookie_header in set_cookies:
            print(f"  {cookie_header}")
            if 'agentid=' in cookie_header:
                agentid_cookies.append(cookie_header)

        # 解析agentid获取验证码文字
        if agentid_cookies:
            # 取第一个agentid cookie
            agentid_cookie = agentid_cookies[0]
            # 提取agentid值
            agentid_start = agentid_cookie.find('agentid=') + 8
            agentid_end = agentid_cookie.find(';', agentid_start)
            if agentid_end == -1:
                agentid_end = len(agentid_cookie)

            agentid_value = agentid_cookie[agentid_start:agentid_end]
            print(f"提取的agentid: {agentid_value}")

            # 分割UUID和编码文字
            if '&' in agentid_value:
                uuid_part, encoded_text = agentid_value.split('&', 1)
                captcha_text = unquote(encoded_text, encoding='utf-8')
                print(f"UUID部分: {uuid_part}")
                print(f"验证码文字: {captcha_text}")

                # 清除旧的agentid并设置新的
                # 先删除所有现有的agentid
                cookies_to_remove = []
                for cookie in session.cookies:
                    if cookie.name == 'agentid':
                        cookies_to_remove.append((cookie.name, cookie.domain, cookie.path))

                for name, domain, path in cookies_to_remove:
                    session.cookies.clear(domain, path, name)

                # 设置新的agentid（只保留UUID部分，去除汉字）
                session.cookies.set('agentid', uuid_part, domain='.mail.10086.cn')
                print(f"更新agentid为UUID: {uuid_part}")

        # 保存验证码图片
        timestamp = int(time.time())
        image_filename = f"captcha_{timestamp}.jpg"

        with open(image_filename, 'wb') as f:
            f.write(response.content)

        print(f"验证码图片已保存: {image_filename}")

        # 打印更新后的重要cookies
        print("更新后的重要Cookies:")
        important_cookies = {}
        for cookie in session.cookies:
            if cookie.name in ['agentid', 'PICTUREUIN', 'PICTURELOGIN']:
                important_cookies[cookie.name] = cookie.value

        for name, value in important_cookies.items():
            print(f"  {name} = {value}")

        print(f"agentid数量: {len([c for c in session.cookies if c.name == 'agentid'])}")

        return image_filename, captcha_text, True

    except Exception as e:
        print(f"第三步请求失败: {e}")
        return None, None, False

def step4_convert_image_to_base64(image_path):
    """
    第四步：将图片转换为base64字符串

    Args:
        image_path: 图片文件路径

    Returns:
        tuple: (base64字符串, 是否成功)
    """
    try:
        print("\n=== 第四步：转换图片为base64 ===")
        print(f"图片路径: {image_path}")

        if not os.path.exists(image_path):
            print("图片文件不存在")
            return None, False

        with open(image_path, 'rb') as image_file:
            image_data = image_file.read()
            base64_string = base64.b64encode(image_data).decode('utf-8')

        print(f"图片大小: {len(image_data)} 字节")
        print(f"Base64长度: {len(base64_string)} 字符")
        print(f"Base64前50字符: {base64_string[:50]}...")

        return base64_string, True

    except Exception as e:
        print(f"第四步转换失败: {e}")
        return None, False

def step5_recognize_captcha(base64_image, captcha_text):
    """
    第五步：调用验证码识别API

    Args:
        base64_image: 图片的base64字符串
        captcha_text: 验证码文字

    Returns:
        tuple: (识别结果坐标列表, 是否成功)
    """
    try:
        print("\n=== 第五步：调用验证码识别API ===")

        api_url = "https://api.geepass.cn/api/recognize/captcha"

        payload = {
            "token": "035909ff833446e3a906f59888080764j2gbxivgapk6czca",
            "type": 30109,
            "center": True,
            "image": base64_image,
            "ques": captcha_text
        }

        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }

        print(f"API URL: {api_url}")
        print(f"验证码文字: {captcha_text}")
        print(f"请求类型: {payload['type']}")

        response = requests.post(
            api_url,
            json=payload,
            headers=headers,
            verify=False,  # 禁用SSL验证
            timeout=30
        )

        print(f"响应状态码: {response.status_code}")

        if response.status_code != 200:
            print(f"API请求失败: {response.text}")
            return None, False

        result = response.json()
        # print(f"API响应: {json.dumps(result, ensure_ascii=False, indent=2)}")

        # 检查响应结构
        if 'data' not in result:
            print("响应中没有data字段")
            return None, False

        # API响应结构是嵌套的: data.data.targets
        if 'data' not in result['data']:
            print("响应data中没有data字段")
            return None, False

        if 'targets' not in result['data']['data']:
            print("响应data.data中没有targets字段")
            return None, False

        targets = result['data']['data']['targets']
        print(f"\n=== 识别结果 ===")
        print(f"识别到 {len(targets)} 个文字坐标:")

        for i, target in enumerate(targets, 1):
            if isinstance(target, list) and len(target) >= 2:
                x, y = target[0], target[1]
                print(f"第{i}个文字坐标: ({x}, {y})")
            else:
                print(f"第{i}个文字坐标格式异常: {target}")

        return targets, True

    except requests.exceptions.RequestException as e:
        print(f"API请求异常: {e}")
        return None, False
    except json.JSONDecodeError as e:
        print(f"JSON解析失败: {e}")
        print(f"响应内容: {response.text}")
        return None, False
    except Exception as e:
        print(f"第五步识别失败: {e}")
        return None, False

def step6_submit_captcha_verification(session, targets, captcha_text, username):
    """
    第六步：提交验证码验证

    Args:
        session: requests.Session对象
        targets: 识别出的坐标列表
        captcha_text: 验证码文字
        username: 用户名

    Returns:
        tuple: (响应对象, 是否成功)
    """
    try:
        print("\n=== 第六步：提交验证码验证 ===")

        # 1. 获取agentid UUID部分
        agentid_uuid = None
        for cookie in session.cookies:
            if cookie.name == 'agentid':
                agentid_uuid = cookie.value
                break

        if not agentid_uuid:
            print("未找到agentid cookie")
            return None, False

        print(f"agentid UUID: {agentid_uuid}")

        # 2. 计算坐标总和
        coordinate_sum = 0
        print("坐标计算:")
        for i, target in enumerate(targets, 1):
            if isinstance(target, list) and len(target) >= 2:
                x, y = int(target[0]), int(target[1])
                coordinate_sum += x + y
                print(f"  第{i}个坐标: ({x}, {y}) -> 和: {x + y}")
            else:
                print(f"  第{i}个坐标格式异常: {target}")
                return None, False

        print(f"坐标总和: {coordinate_sum}")

        # 3. 生成MD5校验码
        checksum_input = agentid_uuid + str(coordinate_sum)
        checksum = hashlib.md5(checksum_input.encode('utf-8')).hexdigest()
        print(f"校验码输入: {checksum_input}")
        print(f"MD5校验码: {checksum}")

        # 4. 构建JSON对象
        verify_obj = {
            "k": captcha_text,
            "p": [{"x": int(target[0]), "y": int(target[1])} for target in targets],
            "c": checksum
        }

        print(f"验证JSON对象: {json.dumps(verify_obj, ensure_ascii=False)}")

        # 5. 双重URL编码
        json_str = json.dumps(verify_obj, separators=(',', ':'), ensure_ascii=False)
        first_encode = quote(json_str, safe='')
        second_encode = quote(first_encode, safe='')

        print(f"第一次编码: {first_encode}")
        print(f"第二次编码: {second_encode}")

        # 6. 构建请求参数
        cguid = generate_cguid()
        randnum = generate_randnum()

        url = f"https://mail.10086.cn/s?func=login:checkNewPictureCode&cguid={cguid}&randnum={randnum}"

        print(f"生成的CGUID: {cguid}")
        print(f"生成的randnum: {randnum}")

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/javascript',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Content-Type': 'application/xml',
            'pragma': 'no-cache',
            'cache-control': 'no-cache',
            'sec-ch-ua-platform': '"Windows"',
            'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'origin': 'https://mail.10086.cn',
            'sec-fetch-site': 'same-origin',
            'sec-fetch-mode': 'cors',
            'sec-fetch-dest': 'empty',
            'referer': 'https://mail.10086.cn/webmail/imgverify/index.html',
            'accept-language': 'zh-CN,zh;q=0.9',
            'priority': 'u=1, i'
        }

        # 7. 构建XML请求体
        xml_body = f'''<object>
  <int name="clientId">1</int>
  <string name="verifyCode">{second_encode}</string>
  <string name="userName">{username}</string>
</object>'''

        print(f"\n=== 请求信息 ===")
        print(f"请求URL: {url}")
        print(f"请求方法: POST")
        print(f"Content-Type: {headers['Content-Type']}")

        print(f"\n=== 请求头 ===")
        for key, value in headers.items():
            print(f"{key}: {value}")

        print(f"\n=== 请求体 ===")
        print(xml_body)

        # 8. 发送请求
        response = session.post(
            url,
            headers=headers,
            data=xml_body,
            verify=False,  # 禁用SSL验证
            timeout=30
        )

        print(f"\n=== 响应信息 ===")
        print(f"响应状态码: {response.status_code}")

        print(f"\n=== 响应头 ===")
        for key, value in response.headers.items():
            print(f"{key}: {value}")

        print(f"\n=== 响应体 ===")
        print(response.text)

        return response, response.status_code == 200, second_encode

    except Exception as e:
        print(f"第六步验证提交失败: {e}")
        return None, False, None

def step7_final_login(session, verify_code, username):
    """
    第七步：最终登录请求

    Args:
        session: requests.Session对象
        verify_code: 验证码字符串（双重编码后的）
        username: 用户名

    Returns:
        tuple: (响应对象, 是否成功, 重定向URL)
    """
    try:
        print("\n=== 第七步：最终登录请求 ===")

        url = "https://mail.10086.cn/s?func=login:picLogin"

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Content-Type': 'application/x-www-form-urlencoded',
            'cache-control': 'max-age=0',
            'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'origin': 'https://mail.10086.cn',
            'upgrade-insecure-requests': '1',
            'sec-fetch-site': 'same-origin',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-user': '?1',
            'sec-fetch-dest': 'document',
            'referer': 'https://mail.10086.cn/webmail/imgverify/index.html',
            'accept-language': 'zh-CN,zh;q=0.9',
            'priority': 'u=0, i'
        }

        # 构建表单数据
        form_data = {
            'VerifyCode': verify_code,
            'u': username,
            's': '1'
        }

        print(f"\n=== 请求信息 ===")
        print(f"请求URL: {url}")
        print(f"请求方法: POST")
        print(f"Content-Type: {headers['Content-Type']}")

        print(f"\n=== 请求头 ===")
        for key, value in headers.items():
            print(f"{key}: {value}")

        print(f"\n=== 表单数据 ===")
        for key, value in form_data.items():
            if key == 'VerifyCode':
                print(f"{key}: {value[:50]}...（已截断）")
            else:
                print(f"{key}: {value}")

        # 发送请求
        response = session.post(
            url,
            headers=headers,
            data=form_data,
            verify=False,  # 禁用SSL验证
            allow_redirects=False,  # 不自动跟随重定向，我们要检查302状态
            timeout=30
        )

        print(f"\n=== 响应信息 ===")
        print(f"响应状态码: {response.status_code}")

        print(f"\n=== 响应头 ===")
        for key, value in response.headers.items():
            print(f"{key}: {value}")

        print(f"\n=== 响应体 ===")
        print(response.text)

        # 检查登录是否成功
        is_success = response.status_code == 302
        redirect_url = response.headers.get('location', '')

        if is_success:
            print(f"\n=== 登录成功 ===")
            print(f"重定向URL: {redirect_url}")

            # 分析设置的cookies
            print(f"\n=== 登录成功后的新Cookies ===")
            important_cookies = ['RMKEY', 'Os_SSo_Sid', 'Login_UserNumber', 'rmUin8881']
            for cookie in session.cookies:
                if cookie.name in important_cookies:
                    print(f"{cookie.name}: {cookie.value}")

            # 检查是否包含邮箱主页URL
            if 'appmail.mail.10086.cn' in redirect_url:
                print("✅ 登录成功！重定向到邮箱主页")
            else:
                print("⚠️ 重定向URL异常，可能登录失败")
        else:
            print(f"\n=== 登录失败 ===")
            print(f"状态码: {response.status_code}")
            print("❌ 登录失败，请检查验证码或其他参数")

        return response, is_success, redirect_url

    except Exception as e:
        print(f"第七步最终登录失败: {e}")
        return None, False, ""

def test_cguid_generation():
    """测试CGUID生成算法"""
    print("=== CGUID生成测试 ===")
    for i in range(5):
        cguid = generate_cguid()
        randnum = generate_randnum()
        print(f"CGUID {i+1}: {cguid} (长度: {len(cguid)})")
        print(f"randnum {i+1}: {randnum}")
        print()

def main():
    """主函数"""
    # 配置参数
    username = "13808015971"
    password = "19890810"

    print("139邮箱登录脚本")
    print("=" * 60)
    print(f"用户名: {username}")
    print(f"密码: {'*' * len(password)}")
    print("SSL验证: 禁用")
    print("=" * 60)

    # 测试CGUID生成
    test_cguid_generation()
    
    # 创建session保持cookies
    session = requests.Session()
    
    # 第一步：访问主页
    response1, success1 = step1_get_main_page(session)
    if not success1:
        print("第一步失败，终止执行")
        return
    
    print("\n" + "=" * 60)
    
    # 第二步：登录请求
    response2, success2 = step2_login_request(session, username, password)
    if not success2:
        print("第二步失败，终止执行")
        return

    print("\n" + "=" * 60)

    # 第三步：获取验证码
    image_path, captcha_text, success3 = step3_get_captcha(session)
    if not success3:
        print("第三步失败，终止执行")
        return

    print("\n" + "=" * 60)

    # 第四步：转换图片为base64
    base64_image, success4 = step4_convert_image_to_base64(image_path)
    if not success4:
        print("第四步失败，终止执行")
        return

    print("\n" + "=" * 60)

    # 第五步：调用验证码识别API
    targets, success5 = step5_recognize_captcha(base64_image, captcha_text)
    if not success5:
        print("第五步失败，终止执行")
        return

    print("\n" + "=" * 60)

    # 第六步：提交验证码验证
    response6, success6, verify_code = step6_submit_captcha_verification(session, targets, captcha_text, username)
    if not success6:
        print("第六步失败，终止执行")
        return

    print("\n" + "=" * 60)

    # 第七步：最终登录
    response7, success7, redirect_url = step7_final_login(session, verify_code, username)
    if not success7:
        print("第七步失败，终止执行")
        return

    print("\n" + "=" * 60)
    print("=== 登录流程完成 ===")

    # 检查JSESSIONID
    jsessionid_cookies = []
    for cookie in session.cookies:
        if cookie.name == 'JSESSIONID':
            jsessionid_cookies.append(cookie.value)

    # 最终统计
    print(f"总Cookie数量: {len(session.cookies)}")
    print(f"验证码图片: {image_path}")
    print(f"验证码文字: {captcha_text}")
    print(f"识别坐标数量: {len(targets) if targets else 0}")
    print(f"重定向URL: {redirect_url}")

    # JSESSIONID状态
    print(f"\n=== JSESSIONID状态 ===")
    if jsessionid_cookies:
        print(f"JSESSIONID存在: 是")
        print(f"JSESSIONID数量: {len(jsessionid_cookies)}")
        for i, jsessionid in enumerate(jsessionid_cookies, 1):
            print(f"JSESSIONID {i}: {jsessionid}")
    else:
        print(f"JSESSIONID存在: 否")

    # 登录状态总结
    if success7 and 'appmail.mail.10086.cn' in redirect_url:
        print("\n🎉 139邮箱登录成功！")
        print("✅ 所有步骤完成")
        print("✅ 获得邮箱访问权限")
    else:
        print("\n❌ 登录可能失败，请检查日志")

    print("\n状态: 完整登录流程结束")

if __name__ == "__main__":
    main()
