#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AES加密解密工具
支持明文加密和密文解密的交互式操作
"""

import json
import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from Crypto.Random import get_random_bytes


class AESCryptoTool:
    """AES加密解密工具类"""
    
    def __init__(self):
        # 使用与服务器端相同的32字节密钥
        self.key = b'139mail_api_2025_secret_key_3200'
        print(f"🔑 使用密钥: {self.key.decode('utf-8')}")
        print(f"🔑 密钥长度: {len(self.key)} 字节")
    
    def encrypt(self, plaintext):
        """加密明文 - 使用ECB模式"""
        try:
            # 如果输入是字符串，转换为字节
            if isinstance(plaintext, str):
                plaintext = plaintext.encode('utf-8')
            
            # 使用ECB模式，不需要IV
            cipher = AES.new(self.key, AES.MODE_ECB)
            
            # 填充并加密
            padded_data = pad(plaintext, AES.block_size)
            encrypted_data = cipher.encrypt(padded_data)
            
            # 直接Base64编码加密数据
            encrypted_base64 = base64.b64encode(encrypted_data).decode('utf-8')
            
            return encrypted_base64
            
        except Exception as e:
            return f"❌ 加密失败: {str(e)}"
    
    def decrypt(self, encrypted_data):
        """解密密文 - 使用ECB模式"""
        try:
            # Base64解码
            encrypted_bytes = base64.b64decode(encrypted_data)
            
            # 使用ECB模式解密，不需要IV
            cipher = AES.new(self.key, AES.MODE_ECB)
            decrypted_padded = cipher.decrypt(encrypted_bytes)
            
            # 去除填充
            decrypted_data = unpad(decrypted_padded, AES.block_size)
            
            # 尝试解析为JSON，如果失败则返回原始字符串
            try:
                return json.loads(decrypted_data.decode('utf-8'))
            except json.JSONDecodeError:
                return decrypted_data.decode('utf-8')
                
        except Exception as e:
            return f"❌ 解密失败: {str(e)}"
    
    def format_output(self, data):
        """格式化输出数据"""
        if isinstance(data, dict):
            return json.dumps(data, ensure_ascii=False, indent=2)
        elif isinstance(data, str):
            return data
        else:
            return str(data)


def main():
    """主函数 - 交互式操作"""
    crypto = AESCryptoTool()
    
    print("\n" + "=" * 60)
    print("🔐 AES加密解密工具")
    print("=" * 60)
    print("📝 操作说明:")
    print("  1. 输入 'e' 或 'encrypt' 进行加密")
    print("  2. 输入 'd' 或 'decrypt' 进行解密")
    print("  3. 输入 'q' 或 'quit' 退出程序")
    print("  4. 输入 'help' 查看帮助")
    print("=" * 60)
    
    while True:
        try:
            print("\n🔧 请选择操作:")
            operation = input(">>> ").strip().lower()
            
            if operation in ['q', 'quit', 'exit']:
                print("👋 再见！")
                break
            
            elif operation in ['h', 'help']:
                print("\n📖 帮助信息:")
                print("  • 加密: 将明文转换为Base64编码的密文")
                print("  • 解密: 将Base64编码的密文转换为明文")
                print("  • 支持中文和特殊字符")
                print("  • 支持JSON格式数据")
                print("  • 使用AES-ECB模式，相同明文产生相同密文")
                
            elif operation in ['e', 'encrypt']:
                print("\n🔒 加密模式")
                print("请输入要加密的明文（支持JSON格式）:")
                plaintext = input("明文>>> ")
                
                if not plaintext.strip():
                    print("⚠️ 明文不能为空")
                    continue
                
                # 尝试解析为JSON，如果失败则作为普通字符串处理
                try:
                    # 检查是否是JSON格式
                    json.loads(plaintext)
                    print("📋 检测到JSON格式数据")
                except json.JSONDecodeError:
                    print("📋 作为普通文本处理")
                
                encrypted = crypto.encrypt(plaintext)
                
                if encrypted.startswith("❌"):
                    print(encrypted)
                else:
                    print(f"\n✅ 加密成功!")
                    print(f"🔐 密文 (Base64): {encrypted}")
                    print(f"📏 密文长度: {len(encrypted)} 字符")
                    
                    # 提供API格式
                    api_format = {"data": encrypted}
                    print(f"\n📡 API请求格式:")
                    print(json.dumps(api_format, ensure_ascii=False, indent=2))
            
            elif operation in ['d', 'decrypt']:
                print("\n🔓 解密模式")
                print("请输入要解密的密文（Base64格式）:")
                ciphertext = input("密文>>> ").strip()
                
                if not ciphertext:
                    print("⚠️ 密文不能为空")
                    continue
                
                # 如果输入的是JSON格式（如API响应），提取data字段
                try:
                    json_data = json.loads(ciphertext)
                    if 'data' in json_data:
                        ciphertext = json_data['data']
                        print("📋 检测到API响应格式，提取data字段")
                except json.JSONDecodeError:
                    print("📋 作为直接密文处理")
                
                decrypted = crypto.decrypt(ciphertext)
                
                if isinstance(decrypted, str) and decrypted.startswith("❌"):
                    print(decrypted)
                else:
                    print(f"\n✅ 解密成功!")
                    print(f"🔓 明文:")
                    print(crypto.format_output(decrypted))
            
            else:
                print("❓ 未知操作，请输入 'e'(加密)、'd'(解密)、'help'(帮助) 或 'q'(退出)")
        
        except KeyboardInterrupt:
            print("\n\n👋 程序被中断，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {str(e)}")


if __name__ == '__main__':
    main()